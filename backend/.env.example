# ============================================
# Kafka Dashboard - Environment Variables
# ============================================
#
# Copy this file to .env and update with your values
# The .env file is ignored by git for security
#

# ===== Server Configuration =====
PORT=5000
NODE_ENV=development

# ===== CORS Configuration =====
CORS_ORIGIN=http://localhost:3000

# ===== MongoDB Configuration =====
# Local development
MONGODB_URI=mongodb://localhost:27017/kafka-dashboard

# Production (example)
# MONGODB_URI=********************************:port/database

# ===== Kafka Configuration =====
# Local development
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=kafka-dashboard-local
KAFKA_GROUP_ID=kafka-dashboard-group-local

# Production (example)
# KAFKA_BROKERS=broker1:9092,broker2:9092,broker3:9092
# KAFKA_CLIENT_ID=kafka-dashboard-prod
# KAFKA_GROUP_ID=kafka-dashboard-group-prod

# ===== Kafka Authentication (Optional) =====
# Uncomment and configure for SASL authentication
# KAFKA_SASL_MECHANISM=PLAIN
# KAFKA_SASL_USERNAME=your-username
# KAFKA_SASL_PASSWORD=your-password

# ===== Kafka SSL (Optional) =====
# Uncomment for SSL connections
# KAFKA_SSL=true

# ===== Kafka Timeouts =====
KAFKA_CONNECTION_TIMEOUT=30000
KAFKA_REQUEST_TIMEOUT=30000

# ===== Rate Limiting =====
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===== Logging =====
LOG_LEVEL=info

# ===== JWT Configuration =====
# Generate a secure random string for production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# ===== Future: AWS Secrets Manager =====
# These will be used when AWS Secrets integration is implemented
# AWS_REGION=us-east-1
# AWS_SECRET_NAME=kafka-dashboard/credentials
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key

# ===== Development Only =====
# These are only used in development
DEBUG=kafka-dashboard:*
