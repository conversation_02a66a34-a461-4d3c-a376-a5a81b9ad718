import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Info,
  Code,
  Security,
  Speed,
  Cloud,
  Group,
  Storage,
  Notifications,
} from '@mui/icons-material';

const AboutDialog = ({ open, onClose }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const features = [
    {
      icon: <Storage />,
      title: 'Topic Management',
      description: 'Create, view, modify, and delete Kafka topics with partition management'
    },
    {
      icon: <Group />,
      title: 'Consumer Groups',
      description: 'Monitor consumer groups, view member details, and track offsets'
    },
    {
      icon: <Notifications />,
      title: 'Message Browser',
      description: 'Search and view messages across topics and partitions in real-time'
    },
    {
      icon: <Code />,
      title: 'Message Producer',
      description: 'Send messages to Kafka topics with custom headers and formatting'
    },
    {
      icon: <Speed />,
      title: 'Real-time Monitoring',
      description: 'Live message streaming and cluster health monitoring via WebSocket'
    },
    {
      icon: <Security />,
      title: 'User Management',
      description: 'Complete user administration with role-based access control'
    },
    {
      icon: <Cloud />,
      title: 'Environment Switching',
      description: 'Dynamic switching between QA and Production environments'
    },
  ];

  const techStack = [
    { name: 'React', version: '18.x', color: 'primary' },
    { name: 'Material-UI', version: '5.x', color: 'secondary' },
    { name: 'Node.js', version: '18.x', color: 'success' },
    { name: 'KafkaJS', version: '2.x', color: 'warning' },
    { name: 'MongoDB', version: '6.x', color: 'info' },
    { name: 'WebSocket', version: 'Latest', color: 'error' },
  ];

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 2,
          maxHeight: '90vh',
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1,
        pb: 1,
        fontSize: { xs: '1.25rem', sm: '1.5rem' }
      }}>
        <Info color="primary" />
        About Kafka Dashboard
      </DialogTitle>
      
      <DialogContent dividers>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Application Info */}
          <Box>
            <Typography variant="h6" gutterBottom color="primary">
              Application Information
            </Typography>
            <Typography variant="body1" paragraph>
              <strong>Kafka Dashboard</strong> is a comprehensive web-based management interface 
              for Apache Kafka clusters, designed specifically for PolicyBazaar's infrastructure. 
              It provides real-time monitoring, topic management, consumer group tracking, 
              and message browsing capabilities.
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              <Chip label="Version 1.0.0" color="primary" size="small" />
              <Chip label="Production Ready" color="success" size="small" />
              <Chip label="Mobile Responsive" color="info" size="small" />
              <Chip label="Real-time" color="warning" size="small" />
            </Box>
          </Box>

          <Divider />

          {/* Key Features */}
          <Box>
            <Typography variant="h6" gutterBottom color="primary">
              Key Features
            </Typography>
            <List dense>
              {features.map((feature, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    {React.cloneElement(feature.icon, { 
                      fontSize: 'small', 
                      color: 'primary' 
                    })}
                  </ListItemIcon>
                  <ListItemText
                    primary={feature.title}
                    secondary={feature.description}
                    primaryTypographyProps={{ 
                      fontSize: '0.875rem', 
                      fontWeight: 600 
                    }}
                    secondaryTypographyProps={{ 
                      fontSize: '0.75rem' 
                    }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>

          <Divider />

          {/* Technology Stack */}
          <Box>
            <Typography variant="h6" gutterBottom color="primary">
              Technology Stack
            </Typography>
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 1,
              mb: 2 
            }}>
              {techStack.map((tech, index) => (
                <Chip
                  key={index}
                  label={`${tech.name} ${tech.version}`}
                  color={tech.color}
                  variant="outlined"
                  size="small"
                />
              ))}
            </Box>
          </Box>

          <Divider />

          {/* Environment Information */}
          <Box>
            <Typography variant="h6" gutterBottom color="primary">
              Environment Support
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>QA Environment:</strong> Single broker setup for testing and development
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>Production Environment:</strong> Multi-broker cluster for high availability
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>Dynamic Switching:</strong> Switch between environments without restart
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>Future Enhancement:</strong> AWS Secrets Manager integration planned
            </Typography>
          </Box>

          <Divider />

          {/* Contact Information */}
          <Box>
            <Typography variant="h6" gutterBottom color="primary">
              Support & Contact
            </Typography>
            <Typography variant="body2" paragraph>
              For technical support, feature requests, or bug reports, please contact:
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>Development Team:</strong> PolicyBazaar Engineering
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>Documentation:</strong> Available in project repository
            </Typography>
            <Typography variant="body2" paragraph>
              • <strong>License:</strong> Internal use - PolicyBazaar
            </Typography>
          </Box>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="contained" fullWidth={isMobile}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AboutDialog;
