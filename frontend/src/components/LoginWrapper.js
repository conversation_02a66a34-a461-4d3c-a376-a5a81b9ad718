import React from 'react';
import { useNavigate } from 'react-router-dom';
import Login from '../pages/Login';
import rbacApi from '../services/rbacApi';

const LoginWrapper = ({ onLogin }) => {
  const navigate = useNavigate();

  const handleLoginSuccess = async (userData, token) => {
    // Call the original onLogin handler
    onLogin(userData, token);

    try {
      // Fetch user permissions to determine redirect
      const response = await rbacApi.getUserPermissions();
      const permissions = response.data;

      // Redirect based on role
      if (permissions.role === 'SUPER_ADMIN') {
        navigate('/dashboard', { replace: true });
      } else {
        // Topic Managers and Topic Viewers go to Topics page
        navigate('/topics', { replace: true });
      }
    } catch (error) {
      console.error('Error fetching permissions for redirect:', error);
      // Default redirect to dashboard
      navigate('/dashboard', { replace: true });
    }
  };

  return <Login onLogin={handleLoginSuccess} />;
};

export default LoginWrapper;
