import api from './api';

const rbacApi = {
  // Get all available roles
  getRoles: async () => {
    const response = await api.get('/auth/roles');
    return response.data;
  },

  // Get current user permissions
  getUserPermissions: async () => {
    const response = await api.get('/auth/permissions');
    return response.data;
  },

  // Update user role and topic assignments
  updateUserRole: async (userId, roleData) => {
    const response = await api.put(`/auth/users/${userId}/role`, roleData);
    return response.data;
  },

  // Register new user with role and topic assignments
  registerUser: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  }
};

export default rbacApi;
