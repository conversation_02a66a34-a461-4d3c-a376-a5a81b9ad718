import React from 'react';
import { usePermissions, PERMISSIONS } from '../contexts/PermissionsContext';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  LinearProgress,
  Chip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Topic,
  GroupWork,
  Storage,
  TrendingUp,
  Error,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
// import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { consumerGroupsApi, clusterApi } from '../services/api';
import { useTopics } from '../hooks/useTopics';

const MetricCard = ({ title, value, icon, color = 'primary', trend }) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  
  return (
    <Card sx={{ height: '100%', minHeight: { xs: 120, sm: 140 } }}>
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography 
              color="textSecondary" 
              gutterBottom 
              variant={isSmallScreen ? "body2" : "h6"}
              sx={{ 
                fontSize: { xs: '0.875rem', sm: '1.25rem' },
                fontWeight: 500,
              }}
            >
              {title}
            </Typography>
            <Typography 
              variant={isSmallScreen ? "h5" : "h4"} 
              component="h2"
              sx={{ 
                fontSize: { xs: '1.5rem', sm: '2.125rem' },
                fontWeight: 600,
                mb: 1,
              }}
            >
              {value}
            </Typography>
            {trend && (
              <Chip
                label={trend}
                size="small"
                color={trend.includes('+') ? 'success' : 'error'}
                sx={{ 
                  mt: 1,
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  height: { xs: 20, sm: 24 },
                }}
              />
            )}
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: { xs: 48, sm: 60 },
              height: { xs: 48, sm: 60 },
              borderRadius: '50%',
              backgroundColor: `${color}.light`,
              color: `${color}.main`,
              flexShrink: 0,
              ml: 2,
            }}
          >
            {React.cloneElement(icon, { 
              fontSize: isSmallScreen ? "medium" : "large" 
            })}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

const Dashboard = () => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const { isSuperAdmin, getAccessibleTopics, loading: permissionsLoading } = usePermissions();

  const { data: topics, isLoading: topicsLoading } = useTopics();
  const { data: consumerGroups, isLoading: consumersLoading } = useQuery('consumer-groups', consumerGroupsApi.getAll);

  // Only Super Admins can access the dashboard
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  if (!isSuperAdmin()) {
    return (
      <Box sx={{ p: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom color="error">
              Access Denied
            </Typography>
            <Typography variant="body1">
              The Dashboard is only available to Super Administrators.
              You can access your assigned topics through the Topics page.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }
  const { data: clusterInfo, isLoading: clusterLoading } = useQuery('cluster-info', clusterApi.getInfo);
  const { data: clusterHealth } = useQuery('cluster-health', clusterApi.getHealth, {
    refetchInterval: 30000,
  });

  // Mock data for charts
  // const mockMessageData = [
  //   { time: '00:00', messages: 120 },
  //   { time: '04:00', messages: 80 },
  //   { time: '08:00', messages: 200 },
  //   { time: '12:00', messages: 450 },
  //   { time: '16:00', messages: 380 },
  //   { time: '20:00', messages: 290 },
  // ];

  const isHealthy = clusterHealth?.data?.status === 'healthy';

  if (topicsLoading || consumersLoading || clusterLoading) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{
      flexGrow: 1,
      height: 'fit-content',
      maxHeight: '100%',
      overflow: 'visible'
    }}>
      <Typography
        variant={isSmallScreen ? "h5" : "h4"}
        sx={{
          mb: { xs: 2, sm: 4 },
          fontSize: { xs: '1.5rem', sm: '2.125rem' },
          fontWeight: 600,
        }}
      >
        Dashboard Overview
      </Typography>

      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 2, sm: 4 } }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Topics"
            value={topics?.data?.length || 0}
            icon={<Topic />}
            color="primary"
            // trend="+2 this week"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Consumer Groups"
            value={consumerGroups?.data?.length || 0}
            icon={<GroupWork />}
            color="secondary"
            // trend="+1 this week"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Brokers"
            value={clusterInfo?.data?.brokers?.length || 0}
            icon={<Storage />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Cluster Status"
            value={isHealthy ? 'Healthy' : 'Unhealthy'}
            icon={isHealthy ? <TrendingUp /> : <Error />}
            color={isHealthy ? 'success' : 'error'}
          />
        </Grid>
      </Grid>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        {/* <Grid item xs={12} md={8}>
          <Paper sx={{ p: { xs: 2, sm: 3 } }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Message Flow (24h)
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockMessageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="messages"
                  stroke="#1976d2"
                  strokeWidth={2}
                  dot={{ fill: '#1976d2' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid> */}

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: { xs: 2, sm: 3 } }}>
            <Typography 
              variant="h6" 
              sx={{ 
                mb: { xs: 1.5, sm: 2 },
                fontSize: { xs: '1.125rem', sm: '1.25rem' },
                fontWeight: 600,
              }}
            >
              Cluster Information
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography 
                variant="body2" 
                color="textSecondary"
                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
              >
                Cluster ID
              </Typography>
              <Typography 
                variant="body1"
                sx={{ 
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  fontWeight: 500,
                  fontFamily: 'monospace',
                }}
              >
                {clusterInfo?.data?.clusterId || 'N/A'}
              </Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography 
                variant="body2" 
                color="textSecondary"
                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
              >
                Brokers
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {clusterInfo?.data?.brokers?.map((broker, index) => (
                  <Chip
                    key={index}
                    label={`${broker.host}:${broker.port}`}
                    size="small"
                    sx={{ 
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      height: { xs: 20, sm: 24 },
                    }}
                  />
                ))}
              </Box>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography 
                variant="body2" 
                color="textSecondary"
                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
              >
                Connection Status
              </Typography>
              <Chip
                label={isHealthy ? 'Connected' : 'Disconnected'}
                color={isHealthy ? 'success' : 'error'}
                size="small"
                sx={{ 
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  height: { xs: 20, sm: 24 },
                }}
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;