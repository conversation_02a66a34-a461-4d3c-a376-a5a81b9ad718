import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { usePermissions, PERMISSIONS } from '../../contexts/PermissionsContext';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Box,
} from '@mui/material';
import {
  Dashboard,
  Topic,
  GroupWork,
  Message,
  Send,
  Storage,
  Settings,
  People,
} from '@mui/icons-material';

const getAllMenuItems = () => [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/',
    permission: PERMISSIONS.VIEW_DASHBOARD,
    roles: ['SUPER_ADMIN']
  },
  {
    text: 'Topics',
    icon: <Topic />,
    path: '/topics',
    permission: PERMISSIONS.VIEW_ASSIGNED_TOPICS,
    roles: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER']
  },
  {
    text: 'Consumer Groups',
    icon: <GroupWork />,
    path: '/consumer-groups',
    permission: PERMISSIONS.VIEW_ASSIGNED_CONSUMER_GROUPS,
    roles: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER']
  },
  {
    text: 'Message Browser',
    icon: <Message />,
    path: '/messages',
    permission: PERMISSIONS.VIEW_MESSAGES,
    roles: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER']
  },
  {
    text: 'Producer',
    icon: <Send />,
    path: '/producer',
    permission: PERMISSIONS.PRODUCE_MESSAGES,
    roles: ['SUPER_ADMIN', 'TOPIC_MANAGER']
  },
  {
    text: 'Cluster Info',
    icon: <Storage />,
    path: '/cluster',
    permission: PERMISSIONS.VIEW_CLUSTER_INFO,
    roles: ['SUPER_ADMIN']
  },
  {
    text: 'User Management',
    icon: <People />,
    path: '/users',
    permission: PERMISSIONS.MANAGE_USERS,
    roles: ['SUPER_ADMIN']
  },
  {
    text: 'Settings',
    icon: <Settings />,
    path: '/settings',
    permission: null, // Settings available to all authenticated users
    roles: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER']
  },
];

const Sidebar = ({ drawerWidth, mobileOpen, onDrawerToggle, isMobile }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { hasPermission, permissions } = usePermissions();

  const handleNavigation = (path) => {
    navigate(path);
    // Close mobile drawer after navigation
    if (isMobile) {
      onDrawerToggle();
    }
  };

  // Filter menu items based on user permissions
  const getVisibleMenuItems = () => {
    if (!permissions) return [];

    const allMenuItems = getAllMenuItems();
    return allMenuItems.filter(item => {
      // Check if user's role is allowed for this menu item
      if (!item.roles.includes(permissions.role)) {
        return false;
      }

      // Check specific permission if required
      if (item.permission && !hasPermission(item.permission)) {
        return false;
      }

      return true;
    });
  };

  const visibleMenuItems = getVisibleMenuItems();

  const drawerContent = (
    <>
      <Toolbar />
      <Box sx={{ overflow: 'auto' }}>
        <List>
          {visibleMenuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(25, 118, 210, 0.08)',
                    borderRight: '3px solid #1976d2',
                  },
                  '&:hover': {
                    backgroundColor: 'rgba(25, 118, 210, 0.04)',
                  },
                  minHeight: { xs: 48, sm: 56 },
                  px: { xs: 2, sm: 3 },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: location.pathname === item.path ? '#1976d2' : 'inherit',
                    minWidth: { xs: 36, sm: 40 },
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  sx={{
                    color: location.pathname === item.path ? '#1976d2' : 'inherit',
                    '& .MuiListItemText-primary': {
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      fontWeight: location.pathname === item.path ? 600 : 400,
                    },
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </>
  );

  return (
    <>
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={onDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            backgroundColor: 'background.paper',
            borderRight: '1px solid rgba(0, 0, 0, 0.12)',
          },
        }}
      >
        {drawerContent}
      </Drawer>
      
      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            backgroundColor: 'background.paper',
            borderRight: '1px solid rgba(0, 0, 0, 0.12)',
          },
        }}
        open
      >
        {drawerContent}
      </Drawer>
    </>
  );
};

export default Sidebar;