import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import { ArrowBack } from '@mui/icons-material';
import { useQuery } from 'react-query';
import { consumerGroupsApi } from '../services/api';

const ConsumerGroupDetail = () => {
  const { groupId } = useParams();
  const navigate = useNavigate();

  const { data: groupDetails, isLoading } = useQuery(
    ['consumer-group', groupId],
    () => consumerGroupsApi.getById(groupId),
    { enabled: !!groupId }
  );

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!groupDetails?.data) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Consumer group not found
      </Alert>
    );
  }

  const group = groupDetails.data;

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/consumer-groups')} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4">{groupId}</Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Group Information
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Group ID:
                  </Typography>
                  <Typography variant="body2">{group.groupId}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    State:
                  </Typography>
                  <Chip
                    label={group.details?.state || 'Unknown'}
                    color={group.details?.state === 'Stable' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Protocol Type:
                  </Typography>
                  <Typography variant="body2">{group.details?.protocolType || 'N/A'}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Members:
                  </Typography>
                  <Typography variant="body2">{group.details?.members?.length || 0}</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Group Members
              </Typography>
              {group.details?.members?.length === 0 ? (
                <Alert severity="info">
                  No active members in this group.
                </Alert>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Member ID</TableCell>
                        <TableCell>Client ID</TableCell>
                        <TableCell>Host</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {group.details?.members?.map((member, index) => (
                        <TableRow key={index}>
                          <TableCell>{member.memberId}</TableCell>
                          <TableCell>{member.clientId}</TableCell>
                          <TableCell>{member.clientHost}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Topic Offsets
              </Typography>
              {group.offsets?.length === 0 ? (
                <Alert severity="info">
                  No offset information available for this group.
                </Alert>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Topic</TableCell>
                        <TableCell>Partition</TableCell>
                        <TableCell>Offset</TableCell>
                        <TableCell>Metadata</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {group.offsets?.map((offset, index) => (
                        <TableRow key={index}>
                          <TableCell>{offset.topic}</TableCell>
                          <TableCell>
                            <Chip
                              label={offset.partition}
                              size="small"
                              color="primary"
                            />
                          </TableCell>
                          <TableCell>{offset.offset}</TableCell>
                          <TableCell>{offset.metadata || 'N/A'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ConsumerGroupDetail; 