import React, { useState, useMemo } from 'react';
import { usePermissions, PERMISSIONS } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  Card,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  TextField,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Stack
} from '@mui/material';
import {
  GroupWork,
  Delete,
  Visibility,
  Refresh,
  Search,
  Clear,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { consumerGroupsApi } from '../services/api';
import { useDebounce } from '../hooks/useDebounce';
import { FixedSizeList as List } from 'react-window';

const ConsumerGroupCard = ({ group, onView, onDelete }) => {
  const theme = useTheme();
  const { hasPermission, isSuperAdmin, isTopicManager } = usePermissions();

  return (
    <Card
      sx={{
        px: 2,
        py: 2,
        display: 'flex',
        flexDirection: 'column',
        gap: 1.5,
        justifyContent: 'space-between',
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-1px)',
          transition: 'all 0.2s ease-in-out'
        }
      }}
    >
      {/* Top row: icon + group ID + actions */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 1,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 0 }}>
          <GroupWork sx={{ color: 'primary.main', fontSize: 28 }} />
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              fontSize: { xs: '1rem', sm: '1.125rem' },
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              maxWidth: '240px',
            }}
          >
            {group.groupId}
          </Typography>
        </Box>

        {/* Action buttons */}
        <Stack direction="row" spacing={0.5}>
          <IconButton size="small" onClick={() => onView(group.groupId)}>
            <Visibility fontSize="small" />
          </IconButton>

          {/* Delete button - only for Super Admin and Topic Managers */}
          {(isSuperAdmin() || isTopicManager()) && hasPermission(PERMISSIONS.DELETE_CONSUMER_GROUPS) && (
            <IconButton
              size="small"
              onClick={() => onDelete(group.groupId)}
              color="error"
            >
              <Delete fontSize="small" />
            </IconButton>
          )}
        </Stack>
      </Box>

      {/* Middle row: status chips */}
      <Stack
        direction="row"
        spacing={1}
        flexWrap="wrap"
        useFlexGap
        alignItems="center"
      >
        <Chip
          label={`State: ${group.state}`}
          size="small"
          color={group.state === 'Stable' ? 'success' : 'warning'}
          variant="outlined"
        />
        <Chip
          label={`Members: ${group.members?.length || 0}`}
          size="small"
          color="primary"
          variant="outlined"
        />
        <Chip
          label={`Protocol: ${group.protocolType}`}
          size="small"
          color="secondary"
          variant="outlined"
        />
      </Stack>

      {/* Bottom row: view details button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>
        <Button
          size="small"
          variant="outlined"
          startIcon={<Visibility fontSize="small" />}
          onClick={() => onView(group.groupId)}
          sx={{
            fontSize: '0.75rem',
            px: 1.5,
            minWidth: 'auto',
            whiteSpace: 'nowrap',
          }}
        >
          View Details
        </Button>
      </Box>
    </Card>
  );
};

const ConsumerGroups = () => {
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const navigate = useNavigate();
  const { hasPermission, isSuperAdmin, isTopicManager, loading: permissionsLoading } = usePermissions();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Check if user has permission to view consumer groups
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!hasPermission(PERMISSIONS.VIEW_ASSIGNED_CONSUMER_GROUPS)) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. You do not have permission to view consumer groups.
        </Alert>
      </Box>
    );
  }

  const { data: consumerGroups, isLoading } = useQuery(
    'consumer-groups',
    consumerGroupsApi.getAll,
    {
      refetchInterval: 30000,
    }
  );

  // Debounce search term to avoid excessive filtering
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Filter consumer groups based on debounced search term
  const filteredGroups = useMemo(() => {
    if (!consumerGroups?.data || !debouncedSearchTerm.trim()) {
      return consumerGroups?.data || [];
    }

    return consumerGroups.data.filter(group =>
      group.groupId.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  }, [consumerGroups?.data, debouncedSearchTerm]);

  const deleteMutation = useMutation(consumerGroupsApi.delete, {
    onSuccess: () => {
      toast.success('Consumer group deleted successfully');
      queryClient.invalidateQueries('consumer-groups');
      setDeleteConfirmOpen(false);
      setGroupToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting consumer group: ${error.message}`);
    },
  });

  const handleDeleteGroup = (groupId) => {
    setGroupToDelete(groupId);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (groupToDelete) {
      deleteMutation.mutate(groupToDelete);
    }
  };

  const handleViewGroup = (groupId) => {
    navigate(`/consumer-groups/${groupId}`);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'stretch', sm: 'center' },
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography
          variant={isMobile ? "h5" : "h4"}
          sx={{
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          Consumer Groups
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={() => queryClient.invalidateQueries('consumer-groups')}
          size={isMobile ? "small" : "medium"}
          fullWidth={isMobile}
        >
          Refresh
        </Button>
      </Box>

      {/* Search Bar */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search consumer groups..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size={isMobile ? "small" : "medium"}
        />
      </Box>

      {/* Results Info */}
      {debouncedSearchTerm && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {filteredGroups.length} consumer group(s) found for "{debouncedSearchTerm}"
          </Typography>
        </Box>
      )}

      {consumerGroups?.data?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No consumer groups found.
        </Alert>
      ) : filteredGroups.length === 0 && debouncedSearchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No consumer groups found matching "{debouncedSearchTerm}". Try a different search term.
        </Alert>
      ) : (
        <List
          height={Math.min(900, window.innerHeight - 300)}
          itemCount={filteredGroups.length}
          itemSize={isMobile ? 160 : 140}
          width="100%"
        >
          {({ index, style }) => {
            const group = filteredGroups[index];
            return (
              <div style={style} key={group.groupId}>
                <ConsumerGroupCard
                  group={group}
                  onView={handleViewGroup}
                  onDelete={handleDeleteGroup}
                />
              </div>
            );
          }}
        </List>
      )}

      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete consumer group "{groupToDelete}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConsumerGroups; 