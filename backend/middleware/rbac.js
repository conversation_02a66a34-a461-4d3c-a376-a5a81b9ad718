const { rbacService, PERMISSIONS } = require('../services/rbacService');
const logger = require('../utils/logger');

/**
 * Middleware to check if user has required permission
 */
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const hasPermission = await rbacService.hasPermission(req.user.id, permission);
      
      if (!hasPermission) {
        logger.warn(`User ${req.user.username} denied access - missing permission: ${permission}`);
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      next();
    } catch (error) {
      logger.error('Error checking permission:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions'
      });
    }
  };
};

/**
 * Middleware to check if user has access to specific topic
 */
const requireTopicAccess = (topicParamName = 'topicName') => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const topicName = req.params[topicParamName] || req.body[topicParamName] || req.query[topicParamName];
      
      if (!topicName) {
        return res.status(400).json({
          success: false,
          message: 'Topic name is required'
        });
      }

      const hasAccess = await rbacService.hasTopicAccess(req.user.id, topicName);
      
      if (!hasAccess) {
        logger.warn(`User ${req.user.username} denied access to topic: ${topicName}`);
        return res.status(403).json({
          success: false,
          message: 'Access denied to this topic'
        });
      }

      // Add topic name to request for use in route handlers
      req.authorizedTopic = topicName;
      next();
    } catch (error) {
      logger.error('Error checking topic access:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking topic access'
      });
    }
  };
};

/**
 * Middleware to filter topics based on user access
 */
const filterTopicsByAccess = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Add user permissions to request for use in route handlers
    req.userPermissions = await rbacService.getUserPermissions(req.user.id);
    next();
  } catch (error) {
    logger.error('Error getting user permissions:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking permissions'
    });
  }
};

/**
 * Middleware to check if user is super admin
 */
const requireSuperAdmin = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    
    if (userPermissions.role !== 'SUPER_ADMIN') {
      logger.warn(`User ${req.user.username} denied super admin access`);
      return res.status(403).json({
        success: false,
        message: 'Super admin access required'
      });
    }

    next();
  } catch (error) {
    logger.error('Error checking super admin access:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking permissions'
    });
  }
};

/**
 * Middleware to check if user can manage topics (create, update, delete)
 */
const requireTopicManagement = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    
    // Only SUPER_ADMIN and TOPIC_MANAGER can manage topics
    if (!['SUPER_ADMIN', 'TOPIC_MANAGER'].includes(userPermissions.role)) {
      logger.warn(`User ${req.user.username} denied topic management access`);
      return res.status(403).json({
        success: false,
        message: 'Topic management access required'
      });
    }

    req.userPermissions = userPermissions;
    next();
  } catch (error) {
    logger.error('Error checking topic management access:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking permissions'
    });
  }
};

/**
 * Helper function to check multiple permissions (OR logic)
 */
const requireAnyPermission = (permissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      let hasAnyPermission = false;
      
      for (const permission of permissions) {
        if (await rbacService.hasPermission(req.user.id, permission)) {
          hasAnyPermission = true;
          break;
        }
      }
      
      if (!hasAnyPermission) {
        logger.warn(`User ${req.user.username} denied access - missing any of permissions: ${permissions.join(', ')}`);
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      next();
    } catch (error) {
      logger.error('Error checking permissions:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions'
      });
    }
  };
};

module.exports = {
  requirePermission,
  requireTopicAccess,
  filterTopicsByAccess,
  requireSuperAdmin,
  requireTopicManagement,
  requireAnyPermission,
  PERMISSIONS
};
