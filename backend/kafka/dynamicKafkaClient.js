const { Kafka, CompressionTypes, CompressionCodecs } = require('kafkajs');
const SnappyCodec = require('kafkajs-snappy');
const environmentService = require('../services/environmentService');
const logger = require('../utils/logger');

// Register Snappy compression codec
CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

class DynamicKafkaClient {
  constructor() {
    this.clients = new Map(); // Store clients for different environments
    this.currentEnvironment = null;
    this.currentClient = null;
  }

  /**
   * Get or create Kafka client for specific environment
   */
  getKafkaClient(environment = null) {
    const env = environment || environmentService.getCurrentEnvironment().key;
    
    if (!this.clients.has(env)) {
      const kafkaConfig = environmentService.getKafkaConfig(env);
      
      const kafka = new Kafka({
        clientId: kafkaConfig.clientId,
        brokers: kafkaConfig.brokers,
        connectionTimeout: kafkaConfig.connectionTimeout,
        requestTimeout: kafkaConfig.requestTimeout,
        ssl: kafkaConfig.ssl,
        sasl: kafkaConfig.sasl
      });
      
      this.clients.set(env, {
        kafka,
        admin: kafka.admin(),
        isConnected: false,
        consumers: new Map(),
        producers: new Map(),
        isConnecting: false
      });
      
      logger.info(`Created Kafka client for environment: ${env}`);
    }
    
    return this.clients.get(env);
  }

  /**
   * Get current active client
   */
  getCurrentClient() {
    const currentEnv = environmentService.getCurrentEnvironment().key;
    
    if (this.currentEnvironment !== currentEnv) {
      this.currentEnvironment = currentEnv;
      this.currentClient = this.getKafkaClient(currentEnv);
    }
    
    return this.currentClient;
  }

  /**
   * Connect to current environment
   */
  async connect() {
    const client = this.getCurrentClient();
    
    if (client.isConnected || client.isConnecting) {
      return;
    }

    try {
      client.isConnecting = true;
      await client.admin.connect();
      client.isConnected = true;
      client.isConnecting = false;
      
      const env = environmentService.getCurrentEnvironment();
      logger.info(`Connected to Kafka - Environment: ${env.name}`);
    } catch (error) {
      client.isConnecting = false;
      logger.error('Failed to connect to Kafka:', error);
      throw error;
    }
  }

  /**
   * Disconnect from current environment
   */
  async disconnect() {
    const client = this.getCurrentClient();
    
    if (!client.isConnected) {
      return;
    }

    try {
      // Disconnect all consumers
      for (const [key, consumer] of client.consumers) {
        try {
          await consumer.disconnect();
        } catch (error) {
          logger.warn(`Error disconnecting consumer ${key}:`, error);
        }
      }
      client.consumers.clear();

      // Disconnect all producers
      for (const [key, producer] of client.producers) {
        try {
          await producer.disconnect();
        } catch (error) {
          logger.warn(`Error disconnecting producer ${key}:`, error);
        }
      }
      client.producers.clear();

      // Disconnect admin
      await client.admin.disconnect();
      client.isConnected = false;
      
      logger.info('Disconnected from Kafka');
    } catch (error) {
      logger.error('Error during Kafka disconnect:', error);
      throw error;
    }
  }

  /**
   * Switch environment and reconnect
   */
  async switchEnvironment(environment) {
    try {
      // Disconnect from current environment
      if (this.currentClient && this.currentClient.isConnected) {
        await this.disconnect();
      }

      // Switch environment in service
      environmentService.switchEnvironment(environment);
      
      // Connect to new environment
      await this.connect();
      
      const env = environmentService.getCurrentEnvironment();
      logger.info(`Successfully switched to environment: ${env.name}`);
      
      return env;
    } catch (error) {
      logger.error('Error switching environment:', error);
      throw error;
    }
  }

  /**
   * Ensure connected to current environment
   */
  async ensureConnected() {
    const client = this.getCurrentClient();
    
    if (!client.isConnected && !client.isConnecting) {
      try {
        await this.connect();
      } catch (error) {
        logger.warn('Auto-reconnect failed:', error);
        throw error;
      }
    }
  }

  /**
   * Get admin client for current environment
   */
  getAdmin() {
    return this.getCurrentClient().admin;
  }

  /**
   * Get or create consumer for current environment
   */
  getConsumer(groupId, options = {}) {
    const client = this.getCurrentClient();
    const key = `${groupId}-${JSON.stringify(options)}`;
    
    if (!client.consumers.has(key)) {
      const consumer = client.kafka.consumer({
        groupId,
        ...options
      });
      client.consumers.set(key, consumer);
    }
    
    return client.consumers.get(key);
  }

  /**
   * Get or create producer for current environment
   */
  getProducer(options = {}) {
    const client = this.getCurrentClient();
    const key = JSON.stringify(options);
    
    if (!client.producers.has(key)) {
      const producer = client.kafka.producer(options);
      client.producers.set(key, producer);
    }
    
    return client.producers.get(key);
  }

  /**
   * Get current environment info
   */
  getCurrentEnvironmentInfo() {
    return environmentService.getCurrentEnvironment();
  }

  /**
   * Clean up all clients
   */
  async cleanup() {
    for (const [env, client] of this.clients) {
      try {
        if (client.isConnected) {
          // Disconnect consumers
          for (const consumer of client.consumers.values()) {
            await consumer.disconnect();
          }

          // Disconnect producers
          for (const producer of client.producers.values()) {
            await producer.disconnect();
          }

          // Disconnect admin
          await client.admin.disconnect();
        }
      } catch (error) {
        logger.warn(`Error cleaning up client for environment ${env}:`, error);
      }
    }

    this.clients.clear();
    this.currentClient = null;
    this.currentEnvironment = null;
  }

  // ===== KAFKA CLIENT COMPATIBILITY METHODS =====
  // These methods provide the same interface as the original kafkaClient

  /**
   * Check if client is healthy
   */
  isHealthy() {
    try {
      const client = this.getCurrentClient();
      return client.isConnected;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get all topics
   */
  async getTopics() {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();
      const topics = await admin.listTopics();
      const topicMetadata = await admin.fetchTopicMetadata({ topics });

      return topicMetadata.topics.map(topic => ({
        name: topic.name,
        partitions: topic.partitions.length,
        replicationFactor: topic.partitions[0]?.replicas?.length || 1,
        configs: {}
      }));
    } catch (error) {
      logger.error('Error fetching topics:', error);
      throw error;
    }
  }

  /**
   * Get topic message count
   */
  async getTopicMessageCount(topicName) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const partitionOffsets = await admin.fetchTopicOffsets(topicName);

      // Calculate total messages and partition details with message counts
      let totalMessages = 0;
      const partitionDetails = partitionOffsets.map(offset => {
        const messageCount = parseInt(offset.offset);
        totalMessages += messageCount;

        return {
          partitionId: offset.partition,
          messageCount,
          highWatermark: offset.high,
          lowWatermark: offset.low
        };
      });

      return {
        topicName,
        totalMessages,
        partitionDetails
      };
    } catch (error) {
      logger.error(`Error fetching message count for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get message counts for multiple topics
   */
  async getTopicsMessageCounts(topicNames) {
    try {
      await this.ensureConnected();

      const results = await Promise.all(
        topicNames.map(async (topicName) => {
          try {
            return await this.getTopicMessageCount(topicName);
          } catch (error) {
            logger.warn(`Failed to get message count for topic ${topicName}:`, error);
            return {
              topic: topicName,
              messageCount: 0,
              partitions: 0,
              error: error.message
            };
          }
        })
      );

      return results;
    } catch (error) {
      logger.error('Error fetching message counts for topics:', error);
      throw error;
    }
  }

  /**
   * Get topics with message counts
   */
  async getTopicsWithMessageCounts() {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();
      const topics = await admin.listTopics();
      const topicMetadata = await admin.fetchTopicMetadata({ topics });

      const topicsWithCounts = await Promise.all(
        topicMetadata.topics.map(async (topic) => {
          try {
            const messageCountData = await this.getTopicMessageCount(topic.name);
            return {
              name: topic.name,
              partitions: topic.partitions.length,
              replicationFactor: topic.partitions[0]?.replicas?.length || 1,
              messageCount: messageCountData.messageCount,
              configs: {}
            };
          } catch (error) {
            logger.warn(`Failed to get message count for topic ${topic.name}:`, error);
            return {
              name: topic.name,
              partitions: topic.partitions.length,
              replicationFactor: topic.partitions[0]?.replicas?.length || 1,
              messageCount: 0,
              configs: {}
            };
          }
        })
      );

      return topicsWithCounts;
    } catch (error) {
      logger.error('Error fetching topics with message counts:', error);
      throw error;
    }
  }

  /**
   * Create a new topic
   */
  async createTopic(topicName, partitions = 1, replicationFactor = 1, configs = {}) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      await admin.createTopics({
        topics: [{
          topic: topicName,
          numPartitions: partitions,
          replicationFactor: replicationFactor,
          configEntries: Object.entries(configs).map(([key, value]) => ({
            name: key,
            value: value.toString()
          }))
        }]
      });

      return {
        success: true,
        message: `Topic '${topicName}' created successfully`,
        topic: topicName,
        partitions,
        replicationFactor
      };
    } catch (error) {
      logger.error(`Error creating topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Delete a topic
   */
  async deleteTopic(topicName) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      await admin.deleteTopics({
        topics: [topicName]
      });

      return {
        success: true,
        message: `Topic '${topicName}' deleted successfully`
      };
    } catch (error) {
      logger.error(`Error deleting topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Add partitions to a topic
   */
  async addPartitions(topicName, partitionCount) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      await admin.createPartitions({
        topicPartitions: [{
          topic: topicName,
          count: partitionCount
        }]
      });

      return {
        success: true,
        message: `Partitions added to topic '${topicName}'`,
        topic: topicName,
        newPartitionCount: partitionCount
      };
    } catch (error) {
      logger.error(`Error adding partitions to topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get topic configuration
   */
  async getTopicConfig(topicName) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const result = await admin.describeConfigs({
        resources: [{
          type: 2, // TOPIC
          name: topicName
        }]
      });

      const configs = {};
      if (result.resources && result.resources.length > 0) {
        result.resources[0].configEntries.forEach(entry => {
          configs[entry.configName] = entry.configValue;
        });
      }

      return {
        topic: topicName,
        configs
      };
    } catch (error) {
      logger.error(`Error fetching config for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Update topic configuration
   */
  async updateTopicConfig(topicName, configs) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      await admin.alterConfigs({
        resources: [{
          type: 2, // TOPIC
          name: topicName,
          configEntries: Object.entries(configs).map(([key, value]) => ({
            name: key,
            value: value.toString()
          }))
        }]
      });

      return {
        success: true,
        message: `Configuration updated for topic '${topicName}'`,
        topic: topicName,
        configs
      };
    } catch (error) {
      logger.error(`Error updating config for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get consumer groups
   */
  async getConsumerGroups() {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();
      const groups = await admin.listGroups();

      const groupDetails = await Promise.all(
        groups.groups.map(async (group) => {
          try {
            const description = await admin.describeGroups([group.groupId]);
            const offsets = await admin.fetchOffsets({
              groupId: group.groupId,
              topics: []
            });

            return {
              groupId: group.groupId,
              state: description.groups[0]?.state || 'Unknown',
              members: description.groups[0]?.members?.length || 0,
              coordinator: description.groups[0]?.coordinator || {},
              offsets: offsets.length
            };
          } catch (error) {
            logger.warn(`Error getting details for group ${group.groupId}:`, error);
            return {
              groupId: group.groupId,
              state: 'Unknown',
              members: 0,
              coordinator: {},
              offsets: 0
            };
          }
        })
      );

      return groupDetails;
    } catch (error) {
      logger.error('Error fetching consumer groups:', error);
      throw error;
    }
  }

  /**
   * Get consumer group details
   */
  async getConsumerGroupDetails(groupId) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const groupDescription = await admin.describeGroups([groupId]);
      const offsets = await admin.fetchOffsets({
        groupId: groupId,
        topics: []
      });

      return {
        groupId,
        state: groupDescription.groups[0]?.state || 'Unknown',
        members: groupDescription.groups[0]?.members || [],
        coordinator: groupDescription.groups[0]?.coordinator || {},
        offsets
      };
    } catch (error) {
      logger.error(`Error fetching consumer group details for ${groupId}:`, error);
      throw error;
    }
  }

  /**
   * Delete consumer group
   */
  async deleteConsumerGroup(groupId) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      await admin.deleteGroups([groupId]);

      return {
        success: true,
        message: `Consumer group '${groupId}' deleted successfully`
      };
    } catch (error) {
      logger.error(`Error deleting consumer group ${groupId}:`, error);
      throw error;
    }
  }

  /**
   * Produce a message to a topic
   */
  async produceMessage(topicName, message) {
    try {
      await this.ensureConnected();
      const producer = this.getProducer();

      await producer.connect();

      const result = await producer.send({
        topic: topicName,
        messages: [{
          key: message.key || null,
          value: message.value,
          headers: message.headers || {}
        }]
      });

      return {
        success: true,
        message: 'Message produced successfully',
        result
      };
    } catch (error) {
      logger.error(`Error producing message to topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get cluster information
   */
  async getClusterInfo() {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const clusterMetadata = await admin.describeCluster();
      const topics = await admin.listTopics();

      return {
        clusterId: clusterMetadata.clusterId || 'unknown',
        brokers: clusterMetadata.brokers.map(broker => ({
          nodeId: broker.nodeId,
          host: broker.host,
          port: broker.port,
          rack: broker.rack
        })),
        topics: topics.length,
        environment: this.getCurrentEnvironmentInfo()
      };
    } catch (error) {
      logger.error('Error fetching cluster info:', error);
      throw error;
    }
  }

  /**
   * Get recent messages from a topic
   */
  async getRecentMessages(topicName, partition = -1, limit = 100) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const topicOffsets = await admin.fetchTopicOffsets(topicName);

      if (!topicOffsets || topicOffsets.length === 0) {
        logger.warn(`No offsets found for topic: ${topicName}`);
        return [];
      }

      const targetPartitions = partition >= 0
        ? topicOffsets.filter(o => o.partition === partition)
        : topicOffsets;

      if (targetPartitions.length === 0) {
        logger.warn(`Partition ${partition} not found in topic: ${topicName}`);
        return [];
      }

      const consumerGroupId = `temp-group-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
      const consumer = this.getConsumer(consumerGroupId, {
        sessionTimeout: 15000,
        heartbeatInterval: 3000,
        maxWaitTimeInMs: 2000
      });

      await consumer.connect();

      const messages = [];
      const messageBufferLimit = limit * 2; // Buffer more than needed, slice later
      const timeoutDuration = 15000; // Max duration to wait

      const startTime = Date.now();
      let seekMode = true;

      // Prepare seekable offsets
      const seeks = [];

      for (const p of targetPartitions) {
        const latest = parseInt(p.offset);
        const earliest = parseInt((await admin.fetchTopicOffsetsByTimestamp(topicName, Date.now() - 7 * 24 * 3600 * 1000))
          .find(o => o.partition === p.partition)?.offset || '0'); // 7 days fallback
        const safeOffset = Math.max(latest - messageBufferLimit, earliest);

        if (safeOffset >= earliest && safeOffset < latest) {
          seeks.push({ partition: p.partition, offset: safeOffset });
        } else {
          seekMode = false;
          break;
        }
      }

      // Subscribe first
      await consumer.subscribe({ topic: topicName, fromBeginning: !seekMode });

      return await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          logger.info(`Timeout reached. Returning ${messages.length} messages from topic ${topicName}`);
          consumer.disconnect().catch(() => {});
          resolve(messages.slice(-limit));
        }, timeoutDuration);

        consumer.run({
          eachMessage: async ({ topic, partition: p, message }) => {
            if (partition >= 0 && p !== partition) return;

            messages.push({
              topic,
              partition: p,
              offset: message.offset,
              key: message.key?.toString() || null,
              value: message.value?.toString() || null,
              timestamp: message.timestamp,
              headers: message.headers || {}
            });

            if (messages.length >= messageBufferLimit) {
              clearTimeout(timeoutId);
              consumer.disconnect().catch(() => {});
              resolve(messages.slice(-limit));
            }
          }
        }).catch(err => {
          clearTimeout(timeoutId);
          consumer.disconnect().catch(() => {});
          reject(err);
        });

        // Seek after consumer.run is set up
        if (seekMode) {
          seeks.forEach(({ partition, offset }) => {
            consumer.seek({ topic: topicName, partition, offset: offset.toString() });
            logger.info(`Seeking partition ${partition} to offset ${offset}`);
          });
        }
      });

    } catch (error) {
      logger.error(`Error in getRecentMessages for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get messages from offset with support for 'latest', 'earliest', or numeric offset
   */
  async getMessagesFromOffset(topicName, partition = -1, startOffset = 'latest', limit = 100) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const topicOffsets = await admin.fetchTopicOffsets(topicName);
      if (!topicOffsets?.length) {
        logger.warn(`No offsets found for topic: ${topicName}`);
        return [];
      }

      const consumerGroupId = `offset-consumer-${Date.now()}-${Math.random().toString(36).slice(2, 6)}`;
      const consumer = this.getConsumer(consumerGroupId, {
        sessionTimeout: 15000,
        heartbeatInterval: 3000,
        maxWaitTimeInMs: 2000,
        allowAutoTopicCreation: false,
      });

      await consumer.connect();

      const messages = [];
      const messageBufferLimit = limit * 2;
      const timeoutDuration = 15000;

      const isEarliest = startOffset === 'earliest';
      const isLatest = startOffset === 'latest';
      const isNumericOffset = !isNaN(parseInt(startOffset));

      // Subscribe
      await consumer.subscribe({ topic: topicName, fromBeginning: isEarliest });

      return await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          logger.info(`Timeout reached. Returning ${messages.length} messages from topic ${topicName}`);
          consumer.disconnect().catch(() => {});
          resolve(messages.slice(0, limit));
        }, timeoutDuration);

        consumer.run({
          eachMessage: async ({ topic, partition: p, message }) => {
            if (partition >= 0 && p !== partition) return;

            messages.push({
              topic,
              partition: p,
              offset: message.offset,
              key: message.key?.toString() || null,
              value: message.value?.toString() || null,
              timestamp: message.timestamp,
              headers: message.headers || {}
            });

            if (messages.length >= messageBufferLimit) {
              clearTimeout(timeoutId);
              consumer.disconnect().catch(() => {});
              resolve(messages.slice(0, limit));
            }
          }
        }).catch(err => {
          clearTimeout(timeoutId);
          consumer.disconnect().catch(() => {});
          reject(err);
        });

        // Handle different offset types
        if (isNumericOffset) {
          const numericOffset = parseInt(startOffset);
          if (partition >= 0) {
            consumer.seek({ topic: topicName, partition, offset: numericOffset.toString() });
          } else {
            // Seek all partitions to the same offset
            topicOffsets.forEach(p => {
              consumer.seek({ topic: topicName, partition: p.partition, offset: numericOffset.toString() });
            });
          }
        } else if (isLatest) {
          // For latest, seek to the end of each partition
          topicOffsets.forEach(p => {
            const latestOffset = Math.max(0, parseInt(p.offset) - Math.ceil(limit / topicOffsets.length));
            consumer.seek({ topic: topicName, partition: p.partition, offset: latestOffset.toString() });
          });
        }
        // For earliest, no seeking needed as fromBeginning: true handles it
      });

    } catch (error) {
      logger.error(`Error fetching messages from offset for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get messages from specific offset
   */
  async getMessages(topicName, partition = 0, offset = 0, limit = 100) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      const topicOffsets = await admin.fetchTopicOffsets(topicName);
      if (!topicOffsets?.length) {
        logger.warn(`No offsets found for topic: ${topicName}`);
        return [];
      }

      const targetPartitions = partition >= 0
        ? topicOffsets.filter(o => o.partition === partition)
        : topicOffsets;

      if (!targetPartitions.length) {
        logger.warn(`Partition ${partition} not found in topic: ${topicName}`);
        return [];
      }

      const messages = [];
      const consumerGroupId = `offset-fetch-${Date.now()}-${Math.random().toString(36).slice(2, 6)}`;
      const consumer = this.getConsumer(consumerGroupId, {
        sessionTimeout: 20000,
        heartbeatInterval: 3000,
        maxWaitTimeInMs: 2000,
        allowAutoTopicCreation: false,
      });

      await consumer.connect();

      const timeoutDuration = 15000;
      let messageCount = 0;

      await consumer.subscribe({ topic: topicName, fromBeginning: false });

      return await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          logger.info(`Timeout reached. Returning ${messages.length} messages from topic ${topicName}`);
          consumer.disconnect().catch(() => {});
          resolve(messages);
        }, timeoutDuration);

        consumer.run({
          eachMessage: async ({ topic, partition: p, message }) => {
            if (partition >= 0 && p !== partition) return;

            messages.push({
              topic,
              partition: p,
              offset: message.offset,
              key: message.key?.toString() || null,
              value: message.value?.toString() || null,
              timestamp: message.timestamp,
              headers: message.headers || {}
            });

            messageCount++;
            if (messageCount >= limit) {
              clearTimeout(timeoutId);
              consumer.disconnect().catch(() => {});
              resolve(messages);
            }
          }
        }).catch(err => {
          clearTimeout(timeoutId);
          consumer.disconnect().catch(() => {});
          reject(err);
        });

        // Seek to the specified offset
        if (offset >= 0) {
          consumer.seek({ topic: topicName, partition, offset: offset.toString() });
          logger.info(`Seeking partition ${partition} to offset ${offset}`);
        }
      });

    } catch (error) {
      logger.error(`Error fetching messages for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Search messages with filters (key, value, timestamp range)
   */
  async searchMessages(topicName, partition = -1, startFrom = 'latest', limit = 100, searchFilters = {}) {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();

      // Get topic offsets to determine the range
      const topicOffsets = await admin.fetchTopicOffsets(topicName);

      if (!topicOffsets || topicOffsets.length === 0) {
        logger.warn(`No offsets found for topic: ${topicName}`);
        return [];
      }

      // If specific partition is requested, filter to that partition
      const targetPartitions = partition >= 0
        ? topicOffsets.filter(o => o.partition === partition)
        : topicOffsets;

      if (targetPartitions.length === 0) {
        logger.warn(`Partition ${partition} not found in topic: ${topicName}`);
        return [];
      }

      const messages = [];

      // Create a unique consumer group for this request
      const consumerGroupId = `temp-consumer-search-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
      const consumer = this.getConsumer(consumerGroupId, {
        sessionTimeout: 30000,
        heartbeatInterval: 3000,
        maxWaitTimeInMs: 5000
      });

      await consumer.connect();

      const timeoutDuration = 30000; // 30 seconds timeout for search
      const maxMessages = limit * 5; // Search through more messages to find matches

      // Subscribe to the topic
      await consumer.subscribe({
        topic: topicName,
        fromBeginning: startFrom === 'earliest'
      });

      return await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          logger.info(`Search timeout reached. Found ${messages.length} matching messages from topic ${topicName}`);
          consumer.disconnect().catch(() => {});
          resolve(messages.slice(0, limit));
        }, timeoutDuration);

        let processedCount = 0;

        consumer.run({
          eachMessage: async ({ topic, partition: p, message }) => {
            if (partition >= 0 && p !== partition) return;

            processedCount++;

            const messageData = {
              topic,
              partition: p,
              offset: message.offset,
              key: message.key?.toString() || null,
              value: message.value?.toString() || null,
              timestamp: message.timestamp,
              headers: message.headers || {}
            };

            // Apply search filters
            let matches = true;

            // Key filter
            if (searchFilters.key && searchFilters.key.trim()) {
              const keyToSearch = messageData.key || '';
              const searchKey = searchFilters.caseSensitive
                ? searchFilters.key
                : searchFilters.key.toLowerCase();
              const messageKey = searchFilters.caseSensitive
                ? keyToSearch
                : keyToSearch.toLowerCase();

              if (!messageKey.includes(searchKey)) {
                matches = false;
              }
            }

            // Value filter
            if (matches && searchFilters.value && searchFilters.value.trim()) {
              const valueToSearch = messageData.value || '';
              const searchValue = searchFilters.caseSensitive
                ? searchFilters.value
                : searchFilters.value.toLowerCase();
              const messageValue = searchFilters.caseSensitive
                ? valueToSearch
                : valueToSearch.toLowerCase();

              if (!messageValue.includes(searchValue)) {
                matches = false;
              }
            }

            // Timestamp filters
            if (matches && (searchFilters.startTimestamp || searchFilters.endTimestamp)) {
              const messageTime = parseInt(messageData.timestamp);

              if (searchFilters.startTimestamp && messageTime < searchFilters.startTimestamp) {
                matches = false;
              }

              if (matches && searchFilters.endTimestamp && messageTime > searchFilters.endTimestamp) {
                matches = false;
              }
            }

            if (matches) {
              messages.push(messageData);

              if (messages.length >= limit) {
                clearTimeout(timeoutId);
                consumer.disconnect().catch(() => {});
                resolve(messages);
              }
            }

            // Stop if we've processed too many messages without finding enough matches
            if (processedCount >= maxMessages) {
              clearTimeout(timeoutId);
              consumer.disconnect().catch(() => {});
              resolve(messages.slice(0, limit));
            }
          }
        }).catch(err => {
          clearTimeout(timeoutId);
          consumer.disconnect().catch(() => {});
          reject(err);
        });

        // Handle seeking for different start positions
        if (startFrom === 'latest') {
          // Seek to recent messages for each partition
          targetPartitions.forEach(p => {
            const latestOffset = Math.max(0, parseInt(p.offset) - Math.ceil(maxMessages / targetPartitions.length));
            consumer.seek({ topic: topicName, partition: p.partition, offset: latestOffset.toString() });
          });
        }
        // For earliest, fromBeginning: true handles it
      });

    } catch (error) {
      logger.error(`Error searching messages in topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Start realtime consumer (simplified version)
   */
  async startRealtimeConsumer(topicName, callback) {
    try {
      await this.ensureConnected();

      // For now, return success - this would need full implementation for real-time features
      return {
        success: true,
        message: `Started realtime consumer for topic ${topicName}`
      };
    } catch (error) {
      logger.error(`Error starting realtime consumer for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Stop realtime consumer (simplified version)
   */
  async stopRealtimeConsumer(topicName) {
    try {
      return {
        success: true,
        message: `Stopped realtime consumer for topic ${topicName}`
      };
    } catch (error) {
      logger.error(`Error stopping realtime consumer for topic ${topicName}:`, error);
      throw error;
    }
  }

  /**
   * Get message flow data (simplified version)
   */
  async getMessageFlowData() {
    try {
      await this.ensureConnected();

      // Return basic data structure
      return {
        hourlyData: [],
        totalMessages: 0,
        peakHour: new Date().getHours(),
        environment: this.getCurrentEnvironmentInfo()
      };
    } catch (error) {
      logger.error('Error fetching message flow data:', error);
      throw error;
    }
  }

  /**
   * Get broker metrics (simplified version)
   */
  async getBrokerMetrics() {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();
      const clusterMetadata = await admin.describeCluster();

      return {
        brokers: clusterMetadata.brokers.map(broker => ({
          nodeId: broker.nodeId,
          host: broker.host,
          port: broker.port,
          cpuUsage: Math.random() * 100, // Mock data
          memoryUsage: Math.random() * 100, // Mock data
          diskUsage: Math.random() * 100 // Mock data
        })),
        environment: this.getCurrentEnvironmentInfo()
      };
    } catch (error) {
      logger.error('Error fetching broker metrics:', error);
      throw error;
    }
  }

  /**
   * Get message rate (simplified version)
   */
  async getMessageRate() {
    try {
      await this.ensureConnected();
      const admin = this.getAdmin();
      const topics = await admin.listTopics();

      return {
        messagesPerSecond: Math.floor(Math.random() * 1000), // Mock data
        totalMessages: Math.floor(Math.random() * 100000), // Mock data
        topicsCount: topics.length,
        timestamp: new Date().toISOString(),
        environment: this.getCurrentEnvironmentInfo()
      };
    } catch (error) {
      logger.error('Error fetching message rate:', error);
      throw error;
    }
  }
}

// Create singleton instance
const dynamicKafkaClient = new DynamicKafkaClient();

module.exports = dynamicKafkaClient;
