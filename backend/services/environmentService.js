const config = require('../config/config');
const logger = require('../utils/logger');

class EnvironmentService {
  constructor() {
    this.availableEnvironments = {
      qa: {
        name: 'QA Environment',
        description: 'Quality Assurance - Single Broker',
        kafka: {
          brokers: ['***********:9092'],
          clientId: 'kafka-dashboard-qa',
          groupId: 'kafka-dashboard-group-qa',
          connectionTimeout: 5000,
          requestTimeout: 30000,
          ssl: false,
          sasl: {
            mechanism: 'plain',
            username: 'bmskfk',
            password: 'bkls76298764'
          }
        }
      },
      prod: {
        name: 'Production Environment',
        description: 'Production - Multiple Brokers',
        kafka: {
          brokers: ['***********:9092', '************:9092', '************:9092'],
          clientId: 'kafka-dashboard-prod',
          groupId: 'kafka-dashboard-group-prod',
          connectionTimeout: 5000,
          requestTimeout: 30000,
          ssl: false,
          sasl: {
            mechanism: 'plain',
            username: 'bmskfk',
            password: 'bms76298764'
          }
        }
      }
    };
    
    this.currentEnvironment = 'prod'; // Default to PROD
  }

  /**
   * Get all available environments
   */
  getAvailableEnvironments() {
    return Object.keys(this.availableEnvironments).map(key => ({
      key,
      name: this.availableEnvironments[key].name,
      description: this.availableEnvironments[key].description,
      brokerCount: this.availableEnvironments[key].kafka.brokers.length
    }));
  }

  /**
   * Get current environment
   */
  getCurrentEnvironment() {
    return {
      key: this.currentEnvironment,
      ...this.availableEnvironments[this.currentEnvironment]
    };
  }

  /**
   * Switch to a different environment
   */
  switchEnvironment(environmentKey) {
    if (!this.availableEnvironments[environmentKey]) {
      throw new Error(`Invalid environment: ${environmentKey}`);
    }

    const previousEnv = this.currentEnvironment;
    this.currentEnvironment = environmentKey;
    
    logger.info(`Environment switched from ${previousEnv} to ${environmentKey}`);
    
    return this.getCurrentEnvironment();
  }

  /**
   * Get Kafka configuration for current environment
   */
  getCurrentKafkaConfig() {
    return this.availableEnvironments[this.currentEnvironment].kafka;
  }

  /**
   * Get Kafka configuration for specific environment
   */
  getKafkaConfig(environmentKey) {
    if (!this.availableEnvironments[environmentKey]) {
      throw new Error(`Invalid environment: ${environmentKey}`);
    }
    return this.availableEnvironments[environmentKey].kafka;
  }

  /**
   * Validate environment key
   */
  isValidEnvironment(environmentKey) {
    return this.availableEnvironments.hasOwnProperty(environmentKey);
  }
}

// Create singleton instance
const environmentService = new EnvironmentService();

module.exports = environmentService;
