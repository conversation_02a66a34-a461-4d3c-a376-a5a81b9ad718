import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications,
  Palette,
  Security,
  Storage,
  Person,
  Info,
  Construction,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { authApi } from '../services/api';

const Settings = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: currentUser } = useQuery('user-profile', authApi.getProfile);

  const comingSoonFeatures = [
    {
      icon: <Notifications />,
      title: 'Notification Preferences',
      description: 'Configure email, desktop, and sound notifications for Kafka events',
      eta: 'Q2 2025'
    },
    {
      icon: <Palette />,
      title: 'Display Customization',
      description: 'Theme selection, language preferences, and refresh intervals',
      eta: 'Q2 2025'
    },
    {
      icon: <Storage />,
      title: 'Dashboard Layout',
      description: 'Customize dashboard views, metrics display, and layout options',
      eta: 'Q3 2025'
    },
    {
      icon: <Security />,
      title: 'Security Settings',
      description: 'Password management, session timeout, and security preferences',
      eta: 'Q3 2025'
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography
        variant={isMobile ? "h5" : "h4"}
        sx={{
          fontSize: { xs: '1.5rem', sm: '2.125rem' },
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: { xs: 2, sm: 4 }
        }}
      >
        <SettingsIcon /> Application Settings
      </Typography>

      <Grid container spacing={3}>
        {/* User Profile Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person /> User Profile
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Username:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {currentUser?.data?.username || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Email:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {currentUser?.data?.email || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Role:</Typography>
                  <Chip
                    label={currentUser?.data?.role || 'N/A'}
                    color={currentUser?.data?.role === 'admin' ? 'primary' : 'default'}
                    size="small"
                    sx={{ textTransform: 'capitalize', fontWeight: 500 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Last Login:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {currentUser?.data?.lastLogin ?
                      new Date(currentUser.data.lastLogin).toLocaleString() : 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">Account Status:</Typography>
                  <Chip
                    label={currentUser?.data?.isActive ? 'Active' : 'Inactive'}
                    color={currentUser?.data?.isActive ? 'success' : 'error'}
                    size="small"
                    sx={{ fontWeight: 500 }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Coming Soon Features */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Construction /> Coming Soon
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                We're working on exciting new features to enhance your experience. Here's what's coming:
              </Typography>

              <List>
                {comingSoonFeatures.map((feature, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      {React.cloneElement(feature.icon, {
                        fontSize: 'small',
                        color: 'action'
                      })}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {feature.title}
                          </Typography>
                          {/* <Chip
                            label={feature.eta}
                            size="small"
                            color="info"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          /> */}
                        </Box>
                      }
                      secondary={feature.description}
                      primaryTypographyProps={{
                        component: 'div'
                      }}
                      secondaryTypographyProps={{
                        fontSize: '0.875rem',
                        color: 'textSecondary'
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Capabilities */}
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body1">
              <strong>Current Settings Available:</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              • <strong>Environment Switching:</strong> Use the environment selector in the navbar to switch between QA and Production
            </Typography>
            <Typography variant="body2">
              • <strong>User Management:</strong> Admins can manage users through the User Management page
            </Typography>
            <Typography variant="body2">
              • <strong>Cluster Information:</strong> View detailed cluster and broker information in the Cluster Info page
            </Typography>
          </Alert>
        </Grid>

        {/* Feedback Section */}
        <Grid item xs={12}>
          <Card sx={{ bgcolor: 'background.default', border: '1px dashed', borderColor: 'divider' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Info /> Have Suggestions?
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                We'd love to hear your feedback on what settings and features would be most valuable to you.
                Your input helps us prioritize development and create a better user experience.
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Contact the development team through your organization's internal channels to share your ideas!
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;