const express = require('express');
const kafkaClient = require('../kafka/dynamicKafkaClient');
const logger = require('../utils/logger');

const router = express.Router();

// GET /api/consumers - List all consumer groups
router.get('/', async (req, res) => {
  try {
    const consumerGroups = await kafkaClient.getConsumerGroups();
    res.json({
      success: true,
      data: consumerGroups,
      count: consumerGroups.length
    });
  } catch (error) {
    logger.error('Error fetching consumer groups:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch consumer groups',
        status: 500
      }
    });
  }
});

// GET /api/consumers/:groupId - Get consumer group details
router.get('/:groupId', async (req, res) => {
  try {
    const { groupId } = req.params;
    const groupDetails = await kafkaClient.getConsumerGroupDetails(groupId);
    res.json({
      success: true,
      data: groupDetails
    });
  } catch (error) {
    logger.error('Error fetching consumer group details:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch consumer group details',
        status: 500
      }
    });
  }
});

// DELETE /api/consumers/:groupId - Delete consumer group
router.delete('/:groupId', async (req, res) => {
  try {
    const { groupId } = req.params;
    const result = await kafkaClient.deleteConsumerGroup(groupId);
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error deleting consumer group:', error);
    res.status(500).json({
      error: {
        message: 'Failed to delete consumer group',
        status: 500
      }
    });
  }
});

module.exports = router; 