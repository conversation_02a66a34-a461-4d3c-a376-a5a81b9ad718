import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Chip,
  Box,
  Avatar,
  Divider,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  Settings,
  Info,
  AccountCircle,
  Logout,
  Person,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { clusterApi } from '../../services/api';
import EnvironmentSelector from '../EnvironmentSelector';
import AboutDialog from '../AboutDialog';

const Navbar = ({ user, onLogout, onDrawerToggle, isMobile }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationAnchorEl, setNotificationAnchorEl] = useState(null);
  const [aboutDialogOpen, setAboutDialogOpen] = useState(false);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();

  const { data: clusterHealth } = useQuery(
    'cluster-health',
    clusterApi.getHealth,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationMenuOpen = (event) => {
    setNotificationAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setNotificationAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    onLogout();
  };

  const handleSettingsClick = () => {
    handleMenuClose();
    navigate('/settings');
  };

  const handleAboutClick = () => {
    handleMenuClose();
    setAboutDialogOpen(true);
  };

  const isHealthy = clusterHealth?.data?.status === 'healthy';

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        background: 'linear-gradient(45deg, #1976d2 30%, #21CBF3 90%)',
      }}
    >
      <Toolbar sx={{ minHeight: { xs: 56, sm: 64 } }}>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={onDrawerToggle}
          sx={{ 
            mr: 2, 
            display: { md: 'none' },
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            }
          }}
        >
          <MenuIcon />
        </IconButton>
        
        <Typography 
          variant={isSmallScreen ? "h6" : "h5"} 
          component="div" 
          sx={{ 
            flexGrow: 1,
            fontSize: { xs: '1rem', sm: '1.25rem' },
            fontWeight: 600,
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}
        >
          {isSmallScreen ? 'Kafka Dashboard' : 'Kafka Dashboard - PolicyBazaar'}
        </Typography>

        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: { xs: 1, sm: 2 },
          flexWrap: 'nowrap',
        }}>
          {/* Environment Selector - Hide on very small screens */}
          {!isSmallScreen && (
            <EnvironmentSelector variant="navbar" />
          )}

          {/* Cluster Status - Hide on very small screens */}
          {!isSmallScreen && (
            <Chip
              label={isHealthy ? 'Connected' : 'Disconnected'}
              color={isHealthy ? 'success' : 'error'}
              size="small"
              variant="outlined"
              sx={{
                color: 'white',
                borderColor: 'white',
                fontSize: '0.75rem',
                height: 24,
              }}
            />
          )}

          <IconButton
            color="inherit"
            onClick={handleNotificationMenuOpen}
            size={isSmallScreen ? "small" : "large"}
            sx={{
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }
            }}
          >
            <Badge badgeContent={0} color="error">
              <Notifications fontSize={isSmallScreen ? "small" : "medium"} />
            </Badge>
          </IconButton>

          <IconButton
            color="inherit"
            onClick={handleProfileMenuOpen}
            size={isSmallScreen ? "small" : "large"}
            sx={{
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }
            }}
          >
            {user?.username ? (
              <Avatar 
                sx={{ 
                  width: { xs: 28, sm: 32 }, 
                  height: { xs: 28, sm: 32 }, 
                  bgcolor: 'primary.main',
                  fontSize: { xs: '12px', sm: '14px' },
                  fontWeight: 'bold'
                }}
              >
                {user.username.charAt(0).toUpperCase()}
              </Avatar>
            ) : (
              <AccountCircle fontSize={isSmallScreen ? "small" : "medium"} />
            )}
          </IconButton>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            elevation: 3,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              minWidth: { xs: 180, sm: 200 },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          {/* User Info Section */}
          <MenuItem disabled>
            <ListItemIcon>
              <Person fontSize="small" />
            </ListItemIcon>
            <ListItemText 
              primary={user?.username || 'User'}
              secondary={user?.email || 'Logged in'}
              primaryTypographyProps={{ fontSize: '0.875rem' }}
              secondaryTypographyProps={{ fontSize: '0.75rem' }}
            />
          </MenuItem>
          
          <Divider />
          
          {/* Settings */}
          <MenuItem onClick={handleSettingsClick}>
            <ListItemIcon>
              <Settings fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="Settings"
              primaryTypographyProps={{ fontSize: '0.875rem' }}
            />
          </MenuItem>

          {/* About */}
          <MenuItem onClick={handleAboutClick}>
            <ListItemIcon>
              <Info fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="About"
              primaryTypographyProps={{ fontSize: '0.875rem' }}
            />
          </MenuItem>
          
          <Divider />
          
          {/* Logout */}
          <MenuItem onClick={handleLogout}>
            <ListItemIcon>
              <Logout fontSize="small" />
            </ListItemIcon>
            <ListItemText 
              primary="Logout" 
              primaryTypographyProps={{ fontSize: '0.875rem' }}
            />
          </MenuItem>
        </Menu>

        <Menu
          anchorEl={notificationAnchorEl}
          open={Boolean(notificationAnchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            elevation: 3,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              minWidth: { xs: 200, sm: 250 },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleMenuClose}>
            <Typography variant="body2" fontSize="0.875rem">
              No new notifications
            </Typography>
          </MenuItem>
        </Menu>

        {/* About Dialog */}
        <AboutDialog
          open={aboutDialogOpen}
          onClose={() => setAboutDialogOpen(false)}
        />
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;