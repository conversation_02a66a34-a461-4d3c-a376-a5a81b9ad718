const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');
const logger = require('../utils/logger');

/**
 * Migration script to update existing users to new RBAC system
 */
async function migrateUsers() {
  try {
    // Check if MongoDB is already connected
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(config.mongodb.uri, config.mongodb.options);
      logger.info('Connected to MongoDB for user migration');
    } else {
      logger.info('Using existing MongoDB connection for user migration');
    }

    // Find all users with old role format
    const usersToMigrate = await User.find({
      role: { $in: ['admin', 'user'] }
    });

    logger.info(`Found ${usersToMigrate.length} users to migrate`);

    for (const user of usersToMigrate) {
      const oldRole = user.role;
      let newRole;
      let hasAllTopicsAccess = false;

      // Map old roles to new roles
      switch (oldRole) {
        case 'admin':
          newRole = 'SUPER_ADMIN';
          hasAllTopicsAccess = true;
          break;
        case 'user':
          newRole = 'TOPIC_VIEWER';
          hasAllTopicsAccess = false;
          break;
        default:
          logger.warn(`Unknown role: ${oldRole} for user: ${user.username}`);
          continue;
      }

      // Update user
      user.role = newRole;
      user.assignedTopics = user.assignedTopics || [];
      user.hasAllTopicsAccess = hasAllTopicsAccess;

      await user.save();
      logger.info(`Migrated user ${user.username} from ${oldRole} to ${newRole}`);
    }

    logger.info('User migration completed successfully');
    
    // Display summary
    const roleCount = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    logger.info('Current role distribution:');
    roleCount.forEach(role => {
      logger.info(`  ${role._id}: ${role.count} users`);
    });

  } catch (error) {
    logger.error('Error during user migration:', error);
    throw error;
  } finally {
    // Don't disconnect if we're using the main server connection
    if (require.main === module) {
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
    }
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateUsers()
    .then(() => {
      logger.info('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = migrateUsers;
