#!/bin/bash

echo "🚀 Setting up Kafka Dashboard for PolicyBazaar..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js (v16+) and try again."
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing root dependencies..."
npm install

echo "📦 Installing backend dependencies..."
cd backend && npm install && cd ..

echo "📦 Installing frontend dependencies..."
cd frontend

# Try normal installation first
if npm install; then
    echo "✅ Frontend dependencies installed successfully"
else
    echo "⚠️ Standard installation failed, trying with --legacy-peer-deps..."
    if npm install --legacy-peer-deps; then
        echo "✅ Frontend dependencies installed with --legacy-peer-deps"
    else
        echo "❌ Failed to install frontend dependencies. Please check the error messages above."
        exit 1
    fi
fi

cd ..

# Create environment file if it doesn't exist
if [ ! -f "backend/.env" ]; then
    echo "⚙️ Creating environment configuration..."
    cat > backend/.env << 'EOF'
# Server Configuration
PORT=5000
NODE_ENV=development

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=kafka-dashboard
KAFKA_GROUP_ID=kafka-dashboard-group

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
EOF
    echo "✅ Environment file created at backend/.env"
else
    echo "⚠️ Environment file already exists"
fi

# Create logs directory
mkdir -p backend/logs

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Ensure Kafka is running"
echo "2. Update backend/.env with your Kafka broker addresses (optional)"
echo "3. Run: npm run dev"
echo "4. Open: http://localhost:3000"
echo "5. Use the environment selector in the UI to switch between QA/Production"
echo ""
echo "Available commands:"
echo "  npm run dev     - Start development servers"
echo "  npm run start   - Start production servers"
echo "  npm run build   - Build for production"
echo "  npm run test    - Run all tests"
echo "  npm run clean   - Clean all dependencies"
echo ""
echo "🌍 Environment Switching:"
echo "  - No more environment-specific scripts!"
echo "  - Switch between QA and Production dynamically in the UI"
echo "  - Login and use the environment selector in the navbar"