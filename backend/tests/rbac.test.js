const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const User = require('../models/User');
const { Role } = require('../models/Role');
const { rbacService } = require('../services/rbacService');
const { generateToken } = require('../middleware/auth');

// Mock the dependencies
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}));

describe('RBAC System', () => {
  let superAdminUser, topicManagerUser, topicViewerUser;
  let superAdminToken, topicManagerToken, topicViewerToken;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect('mongodb://localhost:27017/kafka-dashboard-test', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Initialize RBAC system
    await rbacService.initialize();

    // Create test users
    superAdminUser = new User({
      username: 'superadmin',
      email: '<EMAIL>',
      password: 'password123',
      role: 'SUPER_ADMIN',
      hasAllTopicsAccess: true
    });
    await superAdminUser.save();

    topicManagerUser = new User({
      username: 'topicmanager',
      email: '<EMAIL>',
      password: 'password123',
      role: 'TOPIC_MANAGER',
      assignedTopics: ['test-topic-1', 'test-topic-2'],
      hasAllTopicsAccess: false
    });
    await topicManagerUser.save();

    topicViewerUser = new User({
      username: 'topicviewer',
      email: '<EMAIL>',
      password: 'password123',
      role: 'TOPIC_VIEWER',
      assignedTopics: ['test-topic-1'],
      hasAllTopicsAccess: false
    });
    await topicViewerUser.save();

    // Generate tokens
    superAdminToken = generateToken(superAdminUser._id);
    topicManagerToken = generateToken(topicManagerUser._id);
    topicViewerToken = generateToken(topicViewerUser._id);
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({});
    await Role.deleteMany({});
    await mongoose.connection.close();
  });

  describe('Role Permissions', () => {
    test('Super Admin should have all permissions', async () => {
      const permissions = await rbacService.getUserPermissions(superAdminUser._id);
      
      expect(permissions.role).toBe('SUPER_ADMIN');
      expect(permissions.hasAllTopicsAccess).toBe(true);
      expect(permissions.permissions).toContain('view_dashboard');
      expect(permissions.permissions).toContain('manage_users');
      expect(permissions.permissions).toContain('create_topics');
    });

    test('Topic Manager should have limited permissions', async () => {
      const permissions = await rbacService.getUserPermissions(topicManagerUser._id);
      
      expect(permissions.role).toBe('TOPIC_MANAGER');
      expect(permissions.hasAllTopicsAccess).toBe(false);
      expect(permissions.assignedTopics).toEqual(['test-topic-1', 'test-topic-2']);
      expect(permissions.permissions).toContain('view_assigned_topics');
      expect(permissions.permissions).toContain('produce_messages');
      expect(permissions.permissions).not.toContain('view_dashboard');
      expect(permissions.permissions).not.toContain('manage_users');
    });

    test('Topic Viewer should have read-only permissions', async () => {
      const permissions = await rbacService.getUserPermissions(topicViewerUser._id);
      
      expect(permissions.role).toBe('TOPIC_VIEWER');
      expect(permissions.hasAllTopicsAccess).toBe(false);
      expect(permissions.assignedTopics).toEqual(['test-topic-1']);
      expect(permissions.permissions).toContain('view_assigned_topics');
      expect(permissions.permissions).toContain('view_messages');
      expect(permissions.permissions).not.toContain('produce_messages');
      expect(permissions.permissions).not.toContain('create_topics');
    });
  });

  describe('Topic Access Control', () => {
    test('Super Admin should have access to all topics', async () => {
      const hasAccess1 = await rbacService.hasTopicAccess(superAdminUser._id, 'any-topic');
      const hasAccess2 = await rbacService.hasTopicAccess(superAdminUser._id, 'another-topic');
      
      expect(hasAccess1).toBe(true);
      expect(hasAccess2).toBe(true);
    });

    test('Topic Manager should have access to assigned topics only', async () => {
      const hasAccess1 = await rbacService.hasTopicAccess(topicManagerUser._id, 'test-topic-1');
      const hasAccess2 = await rbacService.hasTopicAccess(topicManagerUser._id, 'test-topic-2');
      const hasAccess3 = await rbacService.hasTopicAccess(topicManagerUser._id, 'unauthorized-topic');
      
      expect(hasAccess1).toBe(true);
      expect(hasAccess2).toBe(true);
      expect(hasAccess3).toBe(false);
    });

    test('Topic Viewer should have access to assigned topics only', async () => {
      const hasAccess1 = await rbacService.hasTopicAccess(topicViewerUser._id, 'test-topic-1');
      const hasAccess2 = await rbacService.hasTopicAccess(topicViewerUser._id, 'test-topic-2');
      
      expect(hasAccess1).toBe(true);
      expect(hasAccess2).toBe(false);
    });
  });

  describe('User Management', () => {
    test('Should update user role and topic assignments', async () => {
      const updatedUser = await rbacService.updateUserRole(topicViewerUser._id, 'TOPIC_MANAGER');
      expect(updatedUser.role).toBe('TOPIC_MANAGER');

      const updatedUserWithTopics = await rbacService.updateUserTopicAssignments(
        topicViewerUser._id, 
        ['test-topic-1', 'test-topic-2', 'test-topic-3'], 
        false
      );
      expect(updatedUserWithTopics.assignedTopics).toEqual(['test-topic-1', 'test-topic-2', 'test-topic-3']);
    });

    test('Should handle all topics access', async () => {
      await rbacService.updateUserTopicAssignments(topicManagerUser._id, [], true);
      
      const allTopics = ['topic-1', 'topic-2', 'topic-3', 'topic-4'];
      const accessibleTopics = await rbacService.getUserAccessibleTopics(topicManagerUser._id, allTopics);
      
      expect(accessibleTopics).toEqual(allTopics);
    });
  });

  describe('Permission Caching', () => {
    test('Should cache and clear user permissions', async () => {
      // First call should fetch from database
      const permissions1 = await rbacService.getUserPermissions(superAdminUser._id);
      
      // Second call should use cache
      const permissions2 = await rbacService.getUserPermissions(superAdminUser._id);
      
      expect(permissions1).toEqual(permissions2);
      
      // Clear cache
      rbacService.clearUserCache(superAdminUser._id);
      
      // Should fetch from database again
      const permissions3 = await rbacService.getUserPermissions(superAdminUser._id);
      expect(permissions3).toEqual(permissions1);
    });
  });
});
