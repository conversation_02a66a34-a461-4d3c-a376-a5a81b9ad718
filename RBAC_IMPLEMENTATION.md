# Role-Based Access Control (RBAC) Implementation

## Overview

This document describes the comprehensive Role-Based Access Control (RBAC) system implemented in Phase 2 of the Kafka Dashboard project. The RBAC system provides fine-grained access control with three distinct roles and topic-level permissions.

## Roles and Permissions

### 1. Super Administrator (`SUPER_ADMIN`)
**Description**: Full access to all features and functionality

**Permissions**:
- ✅ Dashboard access
- ✅ Cluster information access
- ✅ User management (create, update, delete users)
- ✅ All topic operations (create, read, update, delete)
- ✅ All consumer group operations
- ✅ Message production and consumption
- ✅ Environment switching
- ✅ Access to all topics (current and future)

**UI Access**:
- Dashboard
- Topics (all)
- Consumer Groups (all)
- Message Browser (all topics)
- Producer (all topics)
- Cluster Info
- User Management
- Settings

### 2. Topic Manager (`TOPIC_MANAGER`)
**Description**: Can manage assigned topics, view messages, and produce messages (Team Lead role)

**Permissions**:
- ❌ Dashboard access
- ❌ Cluster information access
- ❌ User management
- ✅ Topic operations on assigned topics (read, update, delete, manage partitions)
- ✅ Consumer group operations for assigned topics
- ✅ Message production to assigned topics
- ✅ Message consumption from assigned topics
- ✅ Environment switching
- ✅ Access to assigned topics only

**UI Access**:
- Topics (assigned only)
- Consumer Groups (for assigned topics only)
- Message Browser (assigned topics only)
- Producer (assigned topics only)
- Settings

**Topic Assignment Options**:
- Specific topics assigned by admin
- "All Topics Access" checkbox for users who need access to all topics

### 3. Topic Viewer (`TOPIC_VIEWER`)
**Description**: Read-only access to assigned topics and messages (Team Member role)

**Permissions**:
- ❌ Dashboard access
- ❌ Cluster information access
- ❌ User management
- ✅ Topic viewing for assigned topics (read-only)
- ✅ Consumer group viewing for assigned topics
- ✅ Message viewing from assigned topics
- ❌ Message production
- ❌ Topic creation, update, or deletion
- ✅ Environment switching
- ✅ Access to assigned topics only

**UI Access**:
- Topics (assigned only, read-only)
- Consumer Groups (for assigned topics only, read-only)
- Message Browser (assigned topics only)
- Settings

## Implementation Details

### Backend Components

#### 1. Database Models

**User Model** (`backend/models/User.js`):
```javascript
{
  username: String,
  email: String,
  password: String,
  role: {
    type: String,
    enum: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER'],
    default: 'TOPIC_VIEWER'
  },
  assignedTopics: [String],
  hasAllTopicsAccess: Boolean
}
```

**Role Model** (`backend/models/Role.js`):
```javascript
{
  name: String,
  displayName: String,
  description: String,
  permissions: [String],
  isActive: Boolean
}
```

#### 2. RBAC Service (`backend/services/rbacService.js`)
- User permission management
- Topic access validation
- Permission caching (5-minute TTL)
- Role and topic assignment updates

#### 3. RBAC Middleware (`backend/middleware/rbac.js`)
- `requirePermission(permission)` - Check specific permission
- `requireTopicAccess(topicParamName)` - Validate topic access
- `requireSuperAdmin()` - Super admin only access
- `requireTopicManagement()` - Topic management permissions
- `filterTopicsByAccess()` - Filter topics by user access

#### 4. Migration Script (`backend/scripts/migrateUsers.js`)
- Automatically migrates existing users from old role system
- Maps `admin` → `SUPER_ADMIN`, `user` → `TOPIC_VIEWER`
- Runs automatically on server startup

### Frontend Components

#### 1. Permissions Context (`frontend/src/contexts/PermissionsContext.js`)
- Manages user permissions state
- Provides permission checking functions
- Handles permission caching and refresh

#### 2. RBAC API Service (`frontend/src/services/rbacApi.js`)
- API calls for role management
- User permission fetching
- Role and topic assignment updates

#### 3. Enhanced User Management (`frontend/src/components/UserManagement/UserFormDialog.js`)
- Role selection with descriptions
- Topic assignment interface
- "All Topics Access" checkbox
- Validation and error handling

#### 4. Role-Based Navigation (`frontend/src/components/Layout/Sidebar.js`)
- Dynamic menu filtering based on user role
- Permission-based component visibility

## API Endpoints

### Authentication & User Management
- `GET /api/auth/roles` - Get all available roles (Super Admin only)
- `PUT /api/auth/users/:userId/role` - Update user role and permissions (Super Admin only)
- `GET /api/auth/permissions` - Get current user permissions
- `POST /api/auth/register` - Create new user with role and topic assignments (Super Admin only)

### Protected Endpoints
All API endpoints now include appropriate RBAC middleware:

- **Topics**: Role-based filtering, topic access validation
- **Consumer Groups**: Access based on topic assignments
- **Messages**: Topic access validation for viewing/producing
- **Cluster Info**: Super Admin only
- **User Management**: Super Admin only

## Consumer Group Deletion Policy

**Decision**: Topic Managers can delete consumer groups that consume their assigned topics.

**Rationale**:
- Operational necessity for complete topic lifecycle management
- Scope limitation ensures security (only groups consuming assigned topics)
- Real-world requirement for cleaning up stale/unused consumer groups
- RBAC system prevents unauthorized deletions

## Topic Creation Policy

**Policy**: Only Super Administrators can create topics.

**Rationale**:
- Centralized topic management and governance
- Prevents topic sprawl and naming conflicts
- Ensures proper topic configuration and standards
- Admin assigns access after topic creation

## Migration and Backward Compatibility

### Automatic Migration
- Existing users are automatically migrated on server startup
- `admin` role → `SUPER_ADMIN` with all topics access
- `user` role → `TOPIC_VIEWER` with no topic assignments
- Migration logs provide detailed information about the process

### Database Changes
- New fields added to User model with default values
- Role model created with default roles
- Backward compatibility maintained during transition

## Security Features

### Permission Caching
- 5-minute TTL for performance optimization
- Automatic cache invalidation on role/permission changes
- Memory-efficient caching strategy

### Access Control
- JWT token validation on all protected routes
- Role-based middleware on sensitive endpoints
- Topic-level access validation
- Comprehensive error handling and logging

### Audit Trail
- Detailed logging of permission checks
- User role change logging
- Failed access attempt logging

## Testing

### Unit Tests
- RBAC service functionality
- Permission checking logic
- Role assignment and updates
- Topic access validation

### Integration Tests
- API endpoint protection
- Role-based access scenarios
- User management workflows
- Environment switching with RBAC

## Usage Examples

### Creating a Topic Manager
```javascript
// Super Admin creates a Topic Manager with specific topic access
const userData = {
  username: 'teamlead1',
  email: '<EMAIL>',
  password: 'securepassword',
  role: 'TOPIC_MANAGER',
  assignedTopics: ['user-events', 'order-processing'],
  hasAllTopicsAccess: false
};
```

### Creating a Topic Viewer with All Access
```javascript
// Super Admin creates a Topic Viewer with access to all topics
const userData = {
  username: 'analyst1',
  email: '<EMAIL>',
  password: 'securepassword',
  role: 'TOPIC_VIEWER',
  assignedTopics: [],
  hasAllTopicsAccess: true
};
```

## Future Enhancements

### Planned Features
- Group-based permissions
- Time-based access controls
- Advanced audit logging
- API rate limiting per role
- Custom role creation
- Bulk user management
- LDAP/SSO integration

### Scalability Considerations
- Database indexing for performance
- Permission caching optimization
- Horizontal scaling support
- Multi-tenant architecture preparation

## Troubleshooting

### Common Issues
1. **"Role not found" errors**: Ensure RBAC system is initialized and migration completed
2. **Permission denied**: Check user role and topic assignments
3. **Cache issues**: Clear user cache or restart application
4. **Migration failures**: Check database connectivity and user data integrity

### Debug Commands
```bash
# Run migration manually
node backend/scripts/migrateUsers.js

# Run RBAC tests
npm test -- rbac.test.js

# Check role distribution
# (Available in migration logs)
```

This RBAC implementation provides a robust, scalable, and secure access control system that meets the requirements for enterprise-grade Kafka dashboard management.
