import io from 'socket.io-client';

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

class SocketService {
  constructor() {
    this.socket = null;
    this.listeners = new Map();
  }

  connect() {
    if (this.socket?.connected) {
      return;
    }

    this.socket = io(SOCKET_URL, {
      transports: ['websocket'],
      upgrade: false,
    });

    this.socket.on('connect', () => {
      console.log('Connected to server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  subscribeToTopic(topicName, callback) {
    if (!this.socket) {
      this.connect();
    }

    // Subscribe to topic messages
    this.socket.emit('subscribe-topic', topicName);
    
    // Listen for messages
    this.socket.on('message', callback);
    
    // Store listener for cleanup
    this.listeners.set(topicName, callback);
  }

  unsubscribeFromTopic(topicName) {
    if (!this.socket) {
      return;
    }

    // Unsubscribe from topic
    this.socket.emit('unsubscribe-topic', topicName);
    
    // Remove listener
    const callback = this.listeners.get(topicName);
    if (callback) {
      this.socket.off('message', callback);
      this.listeners.delete(topicName);
    }
  }

  isConnected() {
    return this.socket?.connected || false;
  }
}

export default new SocketService(); 