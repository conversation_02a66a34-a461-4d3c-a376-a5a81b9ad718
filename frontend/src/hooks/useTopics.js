import { useQuery } from 'react-query';
import { topicsApi } from '../services/api';

/**
 * Shared hook for fetching topics data
 * This ensures all components use the same query configuration
 * and prevents duplicate API calls
 */
export const useTopics = (options = {}) => {
  return useQuery(
    'topics',
    topicsApi.getAll,
    {
      // Default configuration that all components will share
      refetchInterval: false, // No automatic refetching
      refetchOnWindowFocus: true, // Refetch when user returns to tab
      staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
      cacheTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
      retry: 2,
      retryDelay: 1000,
      // Allow components to override specific options
      ...options
    }
  );
};
