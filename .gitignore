# ============================================
# Kafka Dashboard - Professional .gitignore
# ============================================

# ===== Dependencies =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# ===== Environment Variables =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ===== Build Outputs =====
build/
dist/
frontend/build/
backend/build/
*.tgz
*.tar.gz

# ===== Logs =====
logs/
*.log
backend/logs/
frontend/logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===== Runtime Data =====
pids/
*.pid
*.seed
*.pid.lock

# ===== Coverage Directory =====
coverage/
*.lcov
.nyc_output/

# ===== Testing =====
.jest/
test-results/
junit.xml

# ===== IDE & Editors =====
.vscode/
.idea/
*.swp
*.swo
*~

# ===== OS Generated Files =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===== Temporary Files =====
*.tmp
*.temp
.cache/
.parcel-cache/

# ===== Database =====
*.sqlite
*.sqlite3
*.db

# ===== Kafka & Monitoring =====
kafka-logs/
zookeeper-data/
*.pid

# ===== Docker =====
.dockerignore
docker-compose.override.yml

# ===== Backup Files =====
*.bak
*.backup
*.orig

# ===== Security =====
*.pem
*.key
*.crt
*.p12
*.pfx

# ===== Application Specific =====
# Keep package-lock.json for consistent dependencies
# Keep yarn.lock for consistent dependencies

# ===== React/Frontend Specific =====
.eslintcache
.stylelintcache

# ===== Node.js Specific =====
.npm
.node_repl_history

# ===== PM2 Process Manager =====
.pm2/

# ===== Serverless =====
.serverless/

# ===== Webpack =====
.webpack/

# ===== Misc =====
*.tsbuildinfo
.vite/
.turbo/

# ===== IMPORTANT NOTES =====
#
# Files that SHOULD be committed:
# - package.json (dependency definitions)
# - package-lock.json (exact dependency versions)
# - yarn.lock (yarn equivalent of package-lock.json)
# - .gitignore (this file)
# - README.md, SCRIPTS.md, PROJECT_STRUCTURE.md
# - All source code files
# - Configuration files (without secrets)
#
# Files that should NEVER be committed:
# - .env files (contain secrets)
# - node_modules/ (can be regenerated)
# - build/ and dist/ (generated files)
# - Log files (runtime generated)
# - IDE-specific files
# - OS-specific files
#
# For production deployment:
# - Use environment variables for secrets
# - Consider AWS Secrets Manager for sensitive data
# - Never commit API keys, passwords, or tokens

