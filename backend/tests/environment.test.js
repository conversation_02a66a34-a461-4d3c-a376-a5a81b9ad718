const request = require('supertest');
const express = require('express');
const environmentRoutes = require('../routes/environment');
const environmentService = require('../services/environmentService');
const kafkaClient = require('../kafka/dynamicKafkaClient');

// Mock the dependencies
jest.mock('../services/environmentService');
jest.mock('../kafka/dynamicKafkaClient');
jest.mock('../middleware/auth', () => ({
  authenticateToken: (req, res, next) => {
    req.user = { username: 'testuser' };
    next();
  }
}));

const app = express();
app.use(express.json());
app.use('/api/environment', environmentRoutes);

describe('Environment API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/environment/environments', () => {
    it('should return available environments', async () => {
      const mockEnvironments = [
        { key: 'qa', name: 'QA Environment', description: 'Quality Assurance - Single Broker', brokerCount: 1 },
        { key: 'prod', name: 'Production Environment', description: 'Production - Multiple Brokers', brokerCount: 3 }
      ];

      environmentService.getAvailableEnvironments.mockReturnValue(mockEnvironments);

      const response = await request(app)
        .get('/api/environment/environments')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockEnvironments);
    });
  });

  describe('GET /api/environment/current', () => {
    it('should return current environment', async () => {
      const mockCurrentEnv = {
        key: 'prod',
        name: 'Production Environment',
        description: 'Production - Multiple Brokers'
      };

      environmentService.getCurrentEnvironment.mockReturnValue(mockCurrentEnv);

      const response = await request(app)
        .get('/api/environment/current')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockCurrentEnv);
    });
  });

  describe('POST /api/environment/switch', () => {
    it('should successfully switch environments', async () => {
      const mockCurrentEnv = { key: 'prod', name: 'Production Environment' };
      const mockNewEnv = { key: 'qa', name: 'QA Environment' };

      environmentService.getCurrentEnvironment.mockReturnValue(mockCurrentEnv);
      environmentService.isValidEnvironment.mockReturnValue(true);
      kafkaClient.switchEnvironment.mockResolvedValue(mockNewEnv);

      const response = await request(app)
        .post('/api/environment/switch')
        .send({ environment: 'qa' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockNewEnv);
      expect(kafkaClient.switchEnvironment).toHaveBeenCalledWith('qa');
    });

    it('should return current environment if switching to same environment', async () => {
      const mockCurrentEnv = { key: 'qa', name: 'QA Environment' };

      environmentService.getCurrentEnvironment.mockReturnValue(mockCurrentEnv);
      environmentService.isValidEnvironment.mockReturnValue(true);

      const response = await request(app)
        .post('/api/environment/switch')
        .send({ environment: 'qa' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Already connected to');
      expect(kafkaClient.switchEnvironment).not.toHaveBeenCalled();
    });

    it('should return 400 for missing environment key', async () => {
      const response = await request(app)
        .post('/api/environment/switch')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Environment key is required');
    });

    it('should return 400 for invalid environment key', async () => {
      environmentService.isValidEnvironment.mockReturnValue(false);

      const response = await request(app)
        .post('/api/environment/switch')
        .send({ environment: 'invalid' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid environment key');
    });

    it('should handle kafka connection errors', async () => {
      const mockCurrentEnv = { key: 'prod', name: 'Production Environment' };
      const connectionError = new Error('Connection failed');

      environmentService.getCurrentEnvironment.mockReturnValue(mockCurrentEnv);
      environmentService.isValidEnvironment.mockReturnValue(true);
      kafkaClient.switchEnvironment.mockRejectedValue(connectionError);

      const response = await request(app)
        .post('/api/environment/switch')
        .send({ environment: 'qa' })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Connection failed');
    });
  });
});
