# Git Ignore Configuration Guide

This document explains the `.gitignore` configuration for the Kafka Dashboard project and provides best practices for version control.

## 📋 Overview

The `.gitignore` file is configured to exclude files that:
- Are generated during build/runtime
- Contain sensitive information
- Are environment-specific
- Can be regenerated from source code
- Are IDE or OS-specific

## 🔍 What's Ignored

### Dependencies
- `node_modules/` - Can be regenerated with `npm install`
- Debug logs from package managers

### Environment Variables
- `.env*` files - Contain sensitive configuration
- Local environment overrides

### Build Outputs
- `build/`, `dist/` - Generated from source code
- Compiled assets and bundles

### Logs
- `backend/logs/` - Runtime generated log files
- All `*.log` files
- Package manager debug logs

### IDE & OS Files
- `.vscode/`, `.idea/` - Editor-specific settings
- `.DS_Store`, `Thumbs.db` - OS-generated files

### Temporary Files
- Cache directories
- Temporary build artifacts
- Coverage reports

## ✅ What's NOT Ignored (Important!)

### Package Lock Files
- `package-lock.json` ✅ **COMMITTED**
- `yarn.lock` ✅ **COMMITTED**

**Why?** These files ensure:
- Consistent dependency versions across environments
- Faster and more reliable installs
- Prevention of "works on my machine" issues
- Reproducible builds in production

### Configuration Files
- `package.json` ✅ **COMMITTED**
- Source code configuration files ✅ **COMMITTED**
- Documentation files ✅ **COMMITTED**

## 🚫 Security Best Practices

### Never Commit
- API keys or passwords
- Database connection strings with credentials
- SSL certificates or private keys
- Environment-specific secrets

### Use Instead
- Environment variables for secrets
- AWS Secrets Manager (planned for future)
- Configuration templates with placeholder values

## 🔧 Environment Variables

### Development
Create a `.env` file in the `backend/` directory:
```bash
# This file is ignored by git
PORT=5000
KAFKA_BROKERS=localhost:9092
MONGODB_URI=mongodb://localhost:27017/kafka-dashboard
```

### Production
Use environment variables or secret management:
```bash
# Set via deployment system
export KAFKA_BROKERS="prod-broker1:9092,prod-broker2:9092"
export MONGODB_URI="mongodb://prod-cluster/kafka-dashboard"
```

## 📁 Directory Structure

```
kafka-dashboard/
├── .gitignore              ✅ Committed
├── package.json            ✅ Committed
├── package-lock.json       ✅ Committed
├── node_modules/           ❌ Ignored
├── backend/
│   ├── .env               ❌ Ignored (if exists)
│   ├── logs/              ❌ Ignored
│   ├── package.json       ✅ Committed
│   └── package-lock.json  ✅ Committed
└── frontend/
    ├── build/             ❌ Ignored
    ├── package.json       ✅ Committed
    └── package-lock.json  ✅ Committed
```

## 🚀 Deployment Considerations

### CI/CD Pipeline
1. **Install dependencies**: `npm ci` (uses package-lock.json)
2. **Build application**: `npm run build`
3. **Deploy built assets**: Only deploy `build/` contents

### Environment Variables
- Use deployment platform's secret management
- Never hardcode secrets in source code
- Plan to migrate to AWS Secrets Manager

## 🔄 Maintenance

### Regular Updates
- Review `.gitignore` when adding new tools
- Update patterns for new file types
- Document any custom ignore patterns

### Team Guidelines
- Never commit sensitive data
- Use `git status` before committing
- Review changes before pushing
- Use meaningful commit messages

## 🆘 Emergency Procedures

### If Secrets Are Committed
1. **Immediately rotate** the compromised secrets
2. **Remove from history**: Use `git filter-branch` or BFG Repo-Cleaner
3. **Force push** the cleaned history
4. **Notify team** to re-clone the repository

### Recovery Commands
```bash
# Remove file from git but keep locally
git rm --cached filename

# Remove from history (use with caution)
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch filename' \
--prune-empty --tag-name-filter cat -- --all
```

## 📚 References

- [Git Documentation](https://git-scm.com/docs/gitignore)
- [GitHub .gitignore Templates](https://github.com/github/gitignore)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)

---

**Remember**: When in doubt, don't commit sensitive files. It's easier to add files later than to remove them from git history.
