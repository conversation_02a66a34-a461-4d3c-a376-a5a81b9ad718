const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const environmentService = require('../services/environmentService');
const kafkaClient = require('../kafka/dynamicKafkaClient');
const logger = require('../utils/logger');

// Get all available environments
router.get('/environments', authenticateToken, async (req, res) => {
  try {
    const environments = environmentService.getAvailableEnvironments();
    
    res.json({
      success: true,
      data: environments
    });
  } catch (error) {
    logger.error('Error fetching environments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch environments'
    });
  }
});

// Get current environment
router.get('/current', authenticateToken, async (req, res) => {
  try {
    const currentEnvironment = environmentService.getCurrentEnvironment();
    
    res.json({
      success: true,
      data: currentEnvironment
    });
  } catch (error) {
    logger.error('Error fetching current environment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch current environment'
    });
  }
});

// Switch environment
router.post('/switch', authenticateToken, async (req, res) => {
  try {
    const { environment } = req.body;

    if (!environment) {
      return res.status(400).json({
        success: false,
        message: 'Environment key is required'
      });
    }

    if (!environmentService.isValidEnvironment(environment)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid environment key'
      });
    }

    // Get current environment before switching
    const currentEnv = environmentService.getCurrentEnvironment();

    // If switching to the same environment, just return current state
    if (currentEnv.key === environment) {
      return res.json({
        success: true,
        message: `Already connected to ${currentEnv.name}`,
        data: currentEnv
      });
    }

    logger.info(`User ${req.user.username} switching from ${currentEnv.key} to ${environment}`);

    // Use the dynamic Kafka client to properly switch environments
    // This will disconnect from current environment and connect to new one
    const newEnvironment = await kafkaClient.switchEnvironment(environment);

    logger.info(`User ${req.user.username} successfully switched to environment: ${environment}`);

    res.json({
      success: true,
      message: `Successfully switched to ${newEnvironment.name}`,
      data: newEnvironment
    });
  } catch (error) {
    logger.error('Error switching environment:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to switch environment'
    });
  }
});

// Test environment connection
router.post('/test-connection', authenticateToken, async (req, res) => {
  try {
    const { environment } = req.body;
    
    if (!environment) {
      return res.status(400).json({
        success: false,
        message: 'Environment key is required'
      });
    }

    if (!environmentService.isValidEnvironment(environment)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid environment key'
      });
    }

    // Get Kafka config for the environment
    const kafkaConfig = environmentService.getKafkaConfig(environment);
    
    // Create a temporary Kafka client to test connection
    const { Kafka } = require('kafkajs');
    const testKafka = new Kafka({
      clientId: `${kafkaConfig.clientId}-test`,
      brokers: kafkaConfig.brokers,
      connectionTimeout: kafkaConfig.connectionTimeout,
      requestTimeout: kafkaConfig.requestTimeout,
      ssl: kafkaConfig.ssl,
      sasl: kafkaConfig.sasl
    });

    const testAdmin = testKafka.admin();
    
    try {
      await testAdmin.connect();
      const metadata = await testAdmin.fetchTopicMetadata();
      await testAdmin.disconnect();
      
      res.json({
        success: true,
        message: 'Connection successful',
        data: {
          environment,
          brokers: kafkaConfig.brokers,
          topicCount: metadata.topics ? metadata.topics.length : 0,
          connectionTime: new Date().toISOString()
        }
      });
    } catch (connectionError) {
      await testAdmin.disconnect().catch(() => {}); // Ignore disconnect errors
      throw connectionError;
    }
    
  } catch (error) {
    logger.error('Error testing environment connection:', error);
    res.status(500).json({
      success: false,
      message: `Connection failed: ${error.message}`,
      data: {
        environment: req.body.environment,
        error: error.message
      }
    });
  }
});

module.exports = router;
