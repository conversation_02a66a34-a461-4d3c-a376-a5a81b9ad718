import React, { useState } from 'react';
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Typography,
  CircularProgress,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  CloudQueue as CloudIcon,
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useEnvironment } from '../contexts/EnvironmentContext';

const EnvironmentSelector = ({ variant = 'navbar' }) => {
  const {
    currentEnvironment,
    availableEnvironments,
    isLoading,
    isSwitching,
    switchEnvironment,
    testConnection,
  } = useEnvironment();

  const [showDetails, setShowDetails] = useState(false);
  const [testingConnection, setTestingConnection] = useState(null);

  const handleEnvironmentChange = (event) => {
    const selectedEnv = event.target.value;
    if (selectedEnv !== currentEnvironment?.key) {
      switchEnvironment(selectedEnv);
    }
  };

  const handleTestConnection = async (envKey) => {
    setTestingConnection(envKey);
    try {
      await testConnection(envKey);
    } finally {
      setTestingConnection(null);
    }
  };

  const getEnvironmentIcon = (envKey) => {
    switch (envKey) {
      case 'prod':
        return <CloudIcon fontSize="small" />;
      case 'qa':
        return <StorageIcon fontSize="small" />;
      default:
        return <StorageIcon fontSize="small" />;
    }
  };

  const getEnvironmentColor = (envKey) => {
    switch (envKey) {
      case 'prod':
        return 'error';
      case 'qa':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CircularProgress size={20} />
        <Typography variant="body2">Loading...</Typography>
      </Box>
    );
  }

  if (variant === 'navbar') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <Select
            value={currentEnvironment?.key || ''}
            onChange={handleEnvironmentChange}
            disabled={isSwitching}
            sx={{
              color: 'white',
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.23)',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.5)',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'white',
              },
              '& .MuiSvgIcon-root': {
                color: 'white',
              },
            }}
            renderValue={(selected) => {
              const env = availableEnvironments.find(e => e.key === selected);
              return (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getEnvironmentIcon(selected)}
                  <Typography variant="body2" sx={{ color: 'white' }}>
                    {env?.name || 'Unknown'}
                  </Typography>
                </Box>
              );
            }}
          >
            {availableEnvironments.map((env) => (
              <MenuItem key={env.key} value={env.key}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                  {getEnvironmentIcon(env.key)}
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2">{env.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {env.description}
                    </Typography>
                  </Box>
                  <Chip
                    label={`${env.brokerCount} broker${env.brokerCount > 1 ? 's' : ''}`}
                    size="small"
                    color={getEnvironmentColor(env.key)}
                    variant="outlined"
                  />
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {isSwitching && (
          <CircularProgress size={16} sx={{ color: 'white' }} />
        )}

        <Tooltip title="Environment Details">
          <IconButton
            size="small"
            onClick={() => setShowDetails(true)}
            sx={{ color: 'white' }}
          >
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* Environment Details Dialog */}
        <Dialog open={showDetails} onClose={() => setShowDetails(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {getEnvironmentIcon(currentEnvironment?.key)}
              Environment Details
            </Box>
          </DialogTitle>
          <DialogContent>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CloudIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Current Environment"
                  secondary={currentEnvironment?.name || 'Unknown'}
                />
                <Chip
                  label={currentEnvironment?.key?.toUpperCase() || 'UNKNOWN'}
                  color={getEnvironmentColor(currentEnvironment?.key)}
                  size="small"
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon>
                  <StorageIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Kafka Brokers"
                  secondary={`${currentEnvironment?.kafka?.brokers?.length || 0} broker(s) configured`}
                />
              </ListItem>

              {currentEnvironment?.kafka?.brokers?.map((broker, index) => (
                <ListItem key={index} sx={{ pl: 4 }}>
                  <ListItemText
                    primary={`Broker ${index + 1}`}
                    secondary={broker}
                  />
                </ListItem>
              ))}
            </List>

            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {availableEnvironments.map((env) => (
                <Button
                  key={env.key}
                  variant={env.key === currentEnvironment?.key ? 'contained' : 'outlined'}
                  size="small"
                  startIcon={
                    testingConnection === env.key ? (
                      <CircularProgress size={16} />
                    ) : (
                      <RefreshIcon />
                    )
                  }
                  onClick={() => handleTestConnection(env.key)}
                  disabled={testingConnection === env.key}
                  color={getEnvironmentColor(env.key)}
                >
                  Test {env.key.toUpperCase()}
                </Button>
              ))}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowDetails(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  }

  // Dashboard variant
  return (
    <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
      <Typography variant="h6" gutterBottom>
        Environment
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <Chip
          icon={getEnvironmentIcon(currentEnvironment?.key)}
          label={currentEnvironment?.name || 'Unknown'}
          color={getEnvironmentColor(currentEnvironment?.key)}
          variant="filled"
        />
        <Typography variant="body2" color="text.secondary">
          {currentEnvironment?.description}
        </Typography>
      </Box>
      
      <FormControl fullWidth size="small">
        <Select
          value={currentEnvironment?.key || ''}
          onChange={handleEnvironmentChange}
          disabled={isSwitching}
        >
          {availableEnvironments.map((env) => (
            <MenuItem key={env.key} value={env.key}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                {getEnvironmentIcon(env.key)}
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body2">{env.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {env.description}
                  </Typography>
                </Box>
                <Chip
                  label={`${env.brokerCount} broker${env.brokerCount > 1 ? 's' : ''}`}
                  size="small"
                  color={getEnvironmentColor(env.key)}
                  variant="outlined"
                />
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default EnvironmentSelector;
