{"name": "kafka-dashboard-backend", "version": "1.0.0", "description": "Backend API for Kafka Dashboard with dynamic environment switching", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.2.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "mongoose": "^8.16.1", "node-cache": "^5.1.2", "redis": "^5.5.6", "snappy": "^7.2.2", "socket.io": "^4.7.2", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.1", "nodemon": "^3.0.1"}, "keywords": ["kafka", "api", "dashboard", "nodejs", "express", "mongodb", "environment-switching"], "author": "PolicyBazaar", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}