const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const http = require('http');
const socketIo = require('socket.io');
const mongoose = require('mongoose');
const config = require('./config/config');
const logger = require('./utils/logger');
const kafkaClient = require('./kafka/dynamicKafkaClient');

// Import routes
const authRoutes = require('./routes/auth');
const topicsRoutes = require('./routes/topics');
const consumersRoutes = require('./routes/consumers');
const producersRoutes = require('./routes/producers');
const clusterRoutes = require('./routes/cluster');
const configRoutes = require('./routes/config');
const environmentRoutes = require('./routes/environment');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: config.server.corsOrigin,
    methods: ['GET', 'POST']
  }
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: config.server.corsOrigin,
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimiting.windowMs,
  max: config.rateLimiting.max,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Request logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/topics', topicsRoutes);
app.use('/api/consumers', consumersRoutes);
app.use('/api/producers', producersRoutes);
app.use('/api/cluster', clusterRoutes);
app.use('/api/config', configRoutes);
app.use('/api/environment', environmentRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    kafka: kafkaClient.isHealthy() ? 'connected' : 'disconnected'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`, err);
  res.status(err.status || 500).json({
    error: {
      message: err.message,
      status: err.status || 500
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Route not found',
      status: 404
    }
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  // Handle topic subscription
  socket.on('subscribe-topic', (topicName) => {
    logger.info(`Client ${socket.id} subscribing to topic: ${topicName}`);
    socket.join(`topic-${topicName}`);
    socket.emit('subscription-confirmed', { topic: topicName });
  });

  // Handle topic unsubscription
  socket.on('unsubscribe-topic', (topicName) => {
    logger.info(`Client ${socket.id} unsubscribing from topic: ${topicName}`);
    socket.leave(`topic-${topicName}`);
    socket.emit('unsubscription-confirmed', { topic: topicName });
  });

  // Handle client disconnect
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });

  // Handle connection errors
  socket.on('error', (error) => {
    logger.error(`Socket error for client ${socket.id}:`, error);
  });
});

// Make io available globally for routes
global.io = io;

// Initialize Kafka client and start server
async function startServer() {
  try {
    // Connect to MongoDB
    try {
      await mongoose.connect(config.mongodb.uri, config.mongodb.options);
      logger.info('MongoDB connected successfully');
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      logger.info('Server will continue running without authentication features.');
    }

    // Start the HTTP server first
    server.listen(config.server.port, () => {
      logger.info('='.repeat(60));
      logger.info(`🚀 Kafka Dashboard Backend Started`);
      logger.info('='.repeat(60));
      logger.info(`Environment: ${config.environment.toUpperCase()}`);
      logger.info(`Port: ${config.server.port}`);
      logger.info(`CORS Origin: ${config.server.corsOrigin}`);
      logger.info(`MongoDB: ${config.mongodb.uri}`);
      logger.info(`Kafka Brokers: ${config.kafka.brokers.join(', ')}`);
      logger.info(`Kafka Client ID: ${config.kafka.clientId}`);
      logger.info(`Kafka Group ID: ${config.kafka.groupId}`);
      logger.info(`SASL Authentication: ${config.kafka.sasl ? 'Enabled' : 'Disabled'}`);
      if (config.kafka.sasl) {
        logger.info(`SASL Username: ${config.kafka.sasl.username}`);
        logger.info(`SASL Mechanism: ${config.kafka.sasl.mechanism}`);
      }
      logger.info(`SSL: ${config.kafka.ssl ? 'Enabled' : 'Disabled'}`);
      logger.info(`Log Level: ${config.logging.level}`);
      logger.info('='.repeat(60));
    });

    // Try to connect to Kafka (non-blocking)
    try {
      await kafkaClient.connect();
      logger.info('Kafka client connected successfully');
    } catch (error) {
      logger.error('Failed to connect to Kafka at startup:', error);
      logger.info('Server will continue running. Kafka connection will be retried when needed.');
    }
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  try {
    await kafkaClient.cleanup();
  } catch (error) {
    logger.error('Error cleaning up Kafka clients:', error);
  }
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  try {
    await kafkaClient.cleanup();
  } catch (error) {
    logger.error('Error cleaning up Kafka clients:', error);
  }
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// Export io for use in other modules
module.exports = { io };

startServer(); 