const express = require('express');
const config = require('../config/config');
const kafkaClient = require('../kafka/dynamicKafkaClient');
const logger = require('../utils/logger');

const router = express.Router();

// GET /api/config - Get application configuration
router.get('/', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        kafka: {
          brokers: config.kafka.brokers,
          clientId: config.kafka.clientId,
          connectionTimeout: config.kafka.connectionTimeout,
          requestTimeout: config.kafka.requestTimeout,
          ssl: config.kafka.ssl,
          sasl: config.kafka.sasl ? { mechanism: config.kafka.sasl.mechanism } : null
        },
        server: {
          port: config.server.port,
          nodeEnv: config.server.nodeEnv,
          corsOrigin: config.server.corsOrigin
        },
        rateLimiting: config.rateLimiting
      }
    });
  } catch (error) {
    logger.error('Error fetching configuration:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch configuration',
        status: 500
      }
    });
  }
});

// GET /api/config/kafka-status - Get Kafka connection status
router.get('/kafka-status', async (req, res) => {
  try {
    const isConnected = kafkaClient.isHealthy();
    res.json({
      success: true,
      data: {
        connected: isConnected,
        brokers: config.kafka.brokers,
        clientId: config.kafka.clientId,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error checking Kafka status:', error);
    res.status(500).json({
      error: {
        message: 'Failed to check Kafka status',
        status: 500
      }
    });
  }
});

module.exports = router; 