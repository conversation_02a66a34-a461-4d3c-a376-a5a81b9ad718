import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  ArrowBack,
  Refresh,
  Add,
  PlayArrow,
  Stop,
  Settings,
  Edit,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
import socketService from '../services/socket';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`topic-tabpanel-${index}`}
    aria-labelledby={`topic-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const MessageTable = ({ messages, loading, metadata }) => (
  <TableContainer component={Paper}>
    <Table>
      <TableHead>
        <TableRow>
          <TableCell>Partition</TableCell>
          <TableCell>Offset</TableCell>
          <TableCell>Key</TableCell>
          <TableCell>Value</TableCell>
          <TableCell>Timestamp</TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {loading ? (
          <TableRow>
            <TableCell colSpan={5} align="center">
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
                <CircularProgress size={24} sx={{ mb: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Fetching messages...
                </Typography>
                {metadata?.startFrom === 'latest' && (
                  <Typography variant="caption" color="text.secondary">
                    Loading most recent messages
                  </Typography>
                )}
              </Box>
            </TableCell>
          </TableRow>
        ) : messages?.length === 0 ? (
          <TableRow>
            <TableCell colSpan={5} align="center">
              <Box sx={{ py: 3 }}>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  No messages found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {metadata?.startFrom === 'latest' 
                    ? 'No recent messages in this topic. Try producing some messages or check if the topic is active.'
                    : 'This topic appears to be empty or the messages are not accessible.'
                  }
                </Typography>
                {metadata && (
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                    Fetched at: {new Date(metadata.timestamp).toLocaleString()}
                  </Typography>
                )}
              </Box>
            </TableCell>
          </TableRow>
        ) : (
          <>
            {messages?.map((message, index) => (
              <TableRow key={`${message.partition}-${message.offset}-${index}`}>
                <TableCell>
                  <Chip label={message.partition} size="small" color="primary" />
                </TableCell>
                <TableCell>{message.offset}</TableCell>
                <TableCell>{message.key || 'null'}</TableCell>
                <TableCell>
                  <Box sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis', wordBreak: 'break-word' }}>
                    {message.value}
                  </Box>
                </TableCell>
                <TableCell>
                  {new Date(parseInt(message.timestamp)).toLocaleString()}
                </TableCell>
              </TableRow>
            ))}
            {metadata && (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography variant="caption" color="text.secondary">
                    Showing {metadata.messageCount} messages (limit: {metadata.limit}) • 
                    Fetched at: {new Date(metadata.timestamp).toLocaleString()}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </>
        )}
      </TableBody>
    </Table>
  </TableContainer>
);

const TopicDetail = () => {
  const { topicName } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const [tabValue, setTabValue] = useState(location.state?.activeTab || 0);
  const [messages, setMessages] = useState([]);
  const [realtimeMessages, setRealtimeMessages] = useState([]);
  const [isRealtime, setIsRealtime] = useState(false);
  const [addPartitionDialogOpen, setAddPartitionDialogOpen] = useState(false);
  const [partitionCount, setPartitionCount] = useState(1);
  const [messageStartFrom, setMessageStartFrom] = useState('latest'); // 'latest' or 'earliest'

  const { data: topic, isLoading } = useQuery(
    ['topic', topicName],
    () => topicsApi.getById(topicName),
    { enabled: !!topicName }
  );

  const { data: messagesData, isLoading: messagesLoading, refetch: refetchMessages } = useQuery(
    ['messages', topicName, messageStartFrom],
    () => topicsApi.getMessages(topicName, { limit: 100, startFrom: messageStartFrom }),
    { 
      enabled: !!topicName,
      retry: 2,
      retryDelay: 1000,
      onError: (error) => {
        console.error('Error fetching messages:', error);
        toast.error('Failed to fetch messages. The topic might have too much data or be temporarily unavailable.');
      }
    }
  );

  const { data: topicConfig, isLoading: configLoading } = useQuery(
    ['topic-config', topicName],
    () => fetch(`/api/topics/${topicName}/config`).then(res => res.json()),
    { enabled: !!topicName }
  );

  const addPartitionMutation = useMutation(
    (data) => topicsApi.addPartitions(topicName, data),
    {
      onSuccess: () => {
        toast.success('Partitions added successfully');
        queryClient.invalidateQueries(['topic', topicName]);
        setAddPartitionDialogOpen(false);
      },
      onError: (error) => {
        toast.error(`Error adding partitions: ${error.message}`);
      },
    }
  );

  useEffect(() => {
    if (messagesData?.data) {
      setMessages(messagesData.data);
    }
  }, [messagesData]);

  useEffect(() => {
    if (isRealtime) {
      // Connect to WebSocket
      socketService.connect();
      
      // Subscribe to topic
      socketService.subscribeToTopic(topicName, (message) => {
        console.log('Received real-time message:', message);
        setRealtimeMessages(prev => [message, ...prev.slice(0, 99)]);
        toast.success(`New message received in ${topicName}`);
      });

      // Start backend real-time consumer
      topicsApi.subscribe(topicName)
        .then(() => {
          console.log(`Subscribed to real-time updates for ${topicName}`);
        })
        .catch(error => {
          console.error('Failed to start real-time subscription:', error);
          toast.error('Failed to start real-time monitoring');
        });
    } else {
      // Unsubscribe
      socketService.unsubscribeFromTopic(topicName);
      topicsApi.unsubscribe(topicName)
        .then(() => {
          console.log(`Unsubscribed from real-time updates for ${topicName}`);
        })
        .catch(error => {
          console.error('Failed to stop real-time subscription:', error);
        });
    }

    return () => {
      if (isRealtime) {
        socketService.unsubscribeFromTopic(topicName);
        topicsApi.unsubscribe(topicName).catch(console.error);
      }
    };
  }, [isRealtime, topicName]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const toggleRealtime = () => {
    setIsRealtime(!isRealtime);
  };

  const handleAddPartitions = () => {
    addPartitionMutation.mutate(partitionCount);
  };

  const handleRefreshMessages = () => {
    refetchMessages();
    setRealtimeMessages([]);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!topic?.data) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Topic not found
      </Alert>
    );
  }

  const displayMessages = isRealtime ? [...realtimeMessages, ...messages] : messages;

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/topics')} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4">{topicName}</Typography>
      </Box>

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Overview" />
          <Tab label="Messages" />
          <Tab label="Partitions" />
          <Tab label="Configuration" />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Topic Information
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="textSecondary">
                      Topic Name:
                    </Typography>
                    <Typography variant="body2">{topic.data.name}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="textSecondary">
                      Partitions:
                    </Typography>
                    <Typography variant="body2">{topic.data.partitions}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="textSecondary">
                      Replication Factor:
                    </Typography>
                    <Typography variant="body2">
                      {topic.data.partitionDetails?.[0]?.replicas?.length || 'N/A'}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Actions
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<Add />}
                    onClick={() => setAddPartitionDialogOpen(true)}
                  >
                    Add Partitions
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Settings />}
                    onClick={() => setTabValue(3)}
                  >
                    View Configuration
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <Button
            variant={isRealtime ? 'contained' : 'outlined'}
            startIcon={isRealtime ? <Stop /> : <PlayArrow />}
            onClick={toggleRealtime}
            color={isRealtime ? 'error' : 'primary'}
          >
            {isRealtime ? 'Stop' : 'Start'} Real-time
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefreshMessages}
            disabled={isRealtime}
          >
            Refresh
          </Button>
          <Button
            variant={messageStartFrom === 'latest' ? 'contained' : 'outlined'}
            onClick={() => setMessageStartFrom('latest')}
            disabled={isRealtime}
            size="small"
          >
            Latest Messages
          </Button>
          <Button
            variant={messageStartFrom === 'earliest' ? 'contained' : 'outlined'}
            onClick={() => setMessageStartFrom('earliest')}
            disabled={isRealtime}
            size="small"
          >
            Earliest Messages
          </Button>
          {isRealtime && (
            <Chip 
              label={`${realtimeMessages.length} new messages`} 
              color="success" 
              size="small" 
            />
          )}
        </Box>
        <MessageTable 
          messages={displayMessages} 
          loading={messagesLoading && !isRealtime} 
          metadata={messagesData?.metadata}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h6">Partitions ({topic.data.partitions})</Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setAddPartitionDialogOpen(true)}
          >
            Add Partitions
          </Button>
        </Box>
        <Grid container spacing={2}>
          {topic.data.partitionDetails?.map((partition) => (
            <Grid item xs={12} md={6} key={partition.partitionId}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Partition {partition.partitionId}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={`Leader: ${partition.leader}`}
                      size="small"
                      color="primary"
                    />
                    <Chip
                      label={`Replicas: ${partition.replicas?.length || 0}`}
                      size="small"
                      color="secondary"
                    />
                    <Chip
                      label={`ISR: ${partition.isr?.length || 0}`}
                      size="small"
                      color="success"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Typography variant="h6" gutterBottom>
          Topic Configuration
        </Typography>
        
        {configLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <CircularProgress />
          </Box>
        ) : topicConfig?.success ? (
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Configuration Settings for {topicName}
              </Typography>
              <List>
                {Object.entries(topicConfig.data.configs).map(([key, config]) => (
                  <React.Fragment key={key}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body1" component="span">
                              {key}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              {config.isDefault && <Chip label="Default" size="small" color="default" />}
                              {config.isSensitive && <Chip label="Sensitive" size="small" color="warning" />}
                              {config.readOnly && <Chip label="Read Only" size="small" color="info" />}
                            </Box>
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" component="span">
                              Value: <strong>{config.value}</strong>
                            </Typography>
                            <br />
                            <Typography variant="caption" color="textSecondary">
                              Source: {config.source}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        ) : (
          <Alert severity="warning">
            Failed to load topic configuration. Please check if the topic exists and you have proper permissions.
          </Alert>
        )}
      </TabPanel>

      <Dialog
        open={addPartitionDialogOpen}
        onClose={() => setAddPartitionDialogOpen(false)}
      >
        <DialogTitle>Add Partitions</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Number of Partitions to Add"
            type="number"
            value={partitionCount}
            onChange={(e) => setPartitionCount(parseInt(e.target.value))}
            inputProps={{ min: 1 }}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddPartitionDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddPartitions} variant="contained">
            Add Partitions
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TopicDetail; 