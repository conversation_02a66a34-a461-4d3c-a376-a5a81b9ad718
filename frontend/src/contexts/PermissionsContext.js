import React, { createContext, useContext, useState, useEffect } from 'react';
import rbacApi from '../services/rbacApi';

const PermissionsContext = createContext();

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};

export const PermissionsProvider = ({ children }) => {
  const [permissions, setPermissions] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is authenticated by checking localStorage
  const isAuthenticated = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  };



  // Fetch user permissions
  const fetchPermissions = async () => {
    if (!isAuthenticated()) {
      setPermissions(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await rbacApi.getUserPermissions();
      setPermissions(response.data);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      setError(error.message || 'Failed to fetch permissions');
      setPermissions(null);
    } finally {
      setLoading(false);
    }
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    if (!permissions) return false;
    return permissions.permissions.includes(permission);
  };

  // Check if user has access to specific topic
  const hasTopicAccess = (topicName) => {
    if (!permissions) return false;
    
    // Super admin has access to all topics
    if (permissions.role === 'SUPER_ADMIN') return true;
    
    // User with all topics access
    if (permissions.hasAllTopicsAccess) return true;
    
    // Check if topic is in assigned topics
    return permissions.assignedTopics.includes(topicName);
  };

  // Check if user is super admin
  const isSuperAdmin = () => {
    return permissions?.role === 'SUPER_ADMIN';
  };

  // Check if user is topic manager
  const isTopicManager = () => {
    return permissions?.role === 'TOPIC_MANAGER';
  };

  // Check if user is topic viewer
  const isTopicViewer = () => {
    return permissions?.role === 'TOPIC_VIEWER';
  };

  // Get user's accessible topics from a list of all topics
  const getAccessibleTopics = (allTopics) => {
    if (!permissions || !allTopics) return [];
    
    // Super admin has access to all topics
    if (permissions.role === 'SUPER_ADMIN') return allTopics;
    
    // User with all topics access
    if (permissions.hasAllTopicsAccess) return allTopics;
    
    // Filter topics based on assigned topics
    return allTopics.filter(topic => 
      permissions.assignedTopics.includes(topic.name || topic)
    );
  };

  // Refresh permissions (useful after role changes)
  const refreshPermissions = () => {
    fetchPermissions();
  };

  useEffect(() => {
    fetchPermissions();

    // Listen for storage changes (login/logout in other tabs)
    const handleStorageChange = (e) => {
      if (e.key === 'token' || e.key === 'user') {
        fetchPermissions();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value = {
    permissions,
    loading,
    error,
    hasPermission,
    hasTopicAccess,
    isSuperAdmin,
    isTopicManager,
    isTopicViewer,
    getAccessibleTopics,
    refreshPermissions
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

// Permission constants (should match backend)
export const PERMISSIONS = {
  // Dashboard permissions
  VIEW_DASHBOARD: 'view_dashboard',
  
  // Cluster permissions
  VIEW_CLUSTER_INFO: 'view_cluster_info',
  
  // User management permissions
  MANAGE_USERS: 'manage_users',
  VIEW_USERS: 'view_users',
  CREATE_USERS: 'create_users',
  UPDATE_USERS: 'update_users',
  DELETE_USERS: 'delete_users',
  
  // Topic permissions
  VIEW_ALL_TOPICS: 'view_all_topics',
  VIEW_ASSIGNED_TOPICS: 'view_assigned_topics',
  CREATE_TOPICS: 'create_topics',
  UPDATE_TOPICS: 'update_topics',
  DELETE_TOPICS: 'delete_topics',
  MANAGE_TOPIC_PARTITIONS: 'manage_topic_partitions',
  
  // Message permissions
  VIEW_MESSAGES: 'view_messages',
  PRODUCE_MESSAGES: 'produce_messages',
  
  // Consumer group permissions
  VIEW_ALL_CONSUMER_GROUPS: 'view_all_consumer_groups',
  VIEW_ASSIGNED_CONSUMER_GROUPS: 'view_assigned_consumer_groups',
  DELETE_CONSUMER_GROUPS: 'delete_consumer_groups',
  
  // Environment permissions
  SWITCH_ENVIRONMENT: 'switch_environment'
};
