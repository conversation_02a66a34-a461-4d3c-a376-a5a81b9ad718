# 🚀 Kafka Dashboard - Scripts Guide

This document explains all available scripts for running and managing the Kafka Dashboard application.

## 📋 Quick Start

### Development Mode
```bash
# Install all dependencies
npm run install

# Start both backend and frontend in development mode
npm run dev
```

### Production Mode
```bash
# Build frontend for production
npm run build

# Start both backend and frontend in production mode
npm run start
```

## 🔧 Available Scripts

### Root Level Scripts (`package.json`)

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run dev` | Start both backend and frontend in development mode | Development |
| `npm run start` | Start both backend and frontend in production mode | Production |
| `npm run build` | Build frontend for production | Deployment |
| `npm run install` | Install dependencies for root, backend, and frontend | Setup |
| `npm run test` | Run tests for both backend and frontend | Testing |
| `npm run clean` | Remove all node_modules folders | Maintenance |
| `npm run clean:install` | Clean and reinstall all dependencies | Troubleshooting |

### Individual Component Scripts

#### Backend Only
```bash
cd backend

# Development with auto-reload
npm run dev

# Production mode
npm start

# Run tests
npm test
npm run test:watch      # Watch mode
npm run test:coverage   # With coverage
```

#### Frontend Only
```bash
cd frontend

# Development server
npm start

# Production build
npm run build

# Run tests
npm test
npm run test:coverage   # With coverage

# Analyze build
npm run analyze
```

## 🌍 Environment Switching

**No more environment-specific scripts!** 🎉

The application now supports **dynamic environment switching** through the UI:

- **QA Environment**: Single Kafka broker
- **Production Environment**: Multiple Kafka brokers
- **Switch anytime**: Use the environment selector in the navbar

### How It Works
1. Start the application with `npm run dev` or `npm run start`
2. Login to the dashboard
3. Use the environment selector in the navbar to switch between QA and Production
4. All data (topics, consumers, messages) will be environment-specific

## 📁 Project Structure

```
kafka-dashboard/
├── package.json          # Root scripts and dependencies
├── backend/
│   ├── package.json      # Backend-specific scripts
│   └── server.js         # Main backend entry point
├── frontend/
│   ├── package.json      # Frontend-specific scripts
│   └── src/              # React application
└── SCRIPTS.md           # This file
```

## 🛠️ Development Workflow

### First Time Setup
```bash
# Clone the repository
git clone <repository-url>
cd kafka-dashboard

# Install all dependencies
npm run install

# Start development servers
npm run dev
```

### Daily Development
```bash
# Start development mode (both backend and frontend)
npm run dev

# The application will be available at:
# Frontend: http://localhost:3000
# Backend API: http://localhost:5000
```

### Testing
```bash
# Run all tests
npm run test

# Run backend tests only
cd backend && npm test

# Run frontend tests only
cd frontend && npm test
```

### Production Deployment
```bash
# Build frontend
npm run build

# Start production servers
npm run start
```

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 3000 and 5000 are available
2. **Dependencies issues**: Run `npm run clean:install`
3. **Environment switching not working**: Check that both backend and frontend are running

### Clean Installation
```bash
# Remove all dependencies and reinstall
npm run clean:install
```

### Individual Component Issues
```bash
# Backend issues
cd backend
rm -rf node_modules
npm install

# Frontend issues
cd frontend
rm -rf node_modules
npm install
```

## 📝 Notes

- **No more QA/Prod scripts**: Environment switching is now handled dynamically in the application
- **Simplified workflow**: Just use `npm run dev` for development and `npm run start` for production
- **Professional structure**: Clear separation of concerns with proper script naming
- **Easy maintenance**: Fewer scripts mean less confusion and easier maintenance

## 🎯 Key Benefits

✅ **Simplified**: Reduced from 13+ scripts to 8 essential scripts
✅ **Professional**: Clear naming conventions and proper structure
✅ **Dynamic**: Environment switching without restarting servers
✅ **Maintainable**: Easy to understand and modify
✅ **Scalable**: Ready for additional environments or features
